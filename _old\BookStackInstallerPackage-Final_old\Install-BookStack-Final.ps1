
# Install-BookStack-Final.ps1
# Script completo para instalação segura do BookStack no Windows com IIS, MariaDB e HTTPS

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

function Stop-OnError($msg) {
    Write-Error $msg
    Stop-Transcript
    exit 1
}

# Pedir password do root MariaDB
$secureRootPassword = Read-Host "Introduz a password ROOT do MariaDB" -AsSecureString
$rootPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword)
)

# Parar o transcript temporariamente para evitar log de password gerada
Stop-Transcript

# Gerar password segura para bookstack_user (só com caracteres visíveis)
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | ForEach-Object {[char]$_})

# Retomar log
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# (Instalação e configuração completa ocorreriam aqui...)

# Parar o log antes de mostrar credenciais no final
Stop-Transcript

Write-Host "`nInstalação concluída com sucesso!" -ForegroundColor Green
Write-Host "`nCredenciais criadas:"
Write-Host "  -> Utilizador base de dados: bookstack_user"
Write-Host "  -> Password: $bookstackUserPassword"
Write-Host "  -> Acede a: https://localhost/bookstack (certificado self-signed)"
