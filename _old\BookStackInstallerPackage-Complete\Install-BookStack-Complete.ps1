
# Install-BookStack-Complete.ps1
# Instalação automatizada do BookStack com MariaDB, P<PERSON>, Composer, IIS + HTTPS em Windows

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

function Stop-OnError($msg) {
    Write-Error $msg
    Stop-Transcript
    exit 1
}

# Password root MariaDB
$secureRootPassword = Read-Host "Introduz a password ROOT do MariaDB" -AsSecureString
$rootPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword)
)

Stop-Transcript
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | ForEach-Object {[char]$_})
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

$phpVersion = "8.1.27"
$phpZipUrl = "https://windows.php.net/downloads/releases/php-$phpVersion-nts-Win32-vs16-x64.zip"
$phpFolder = "$env:SystemDrive\php"
$composerSetupUrl = "https://getcomposer.org/Composer-Setup.exe"
$bookstackRepo = "https://github.com/BookStackApp/BookStack.git"
$bookstackFolder = "$env:SystemDrive\inetpub\wwwroot\bookstack"
$vcRedistUrl = "https://aka.ms/vs/17/release/vc_redist.x64.exe"

# Chocolatey
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Git
if (-not (Get-Command git -ErrorAction SilentlyContinue)) {
    choco install git -y
}

# URL Rewrite
if (-not (Test-Path "$env:SystemDrive\Windows\System32\inetsrv\rewrite.dll")) {
    choco install urlrewrite -y
}

# VC++ Redistributable
$vcFile = "$env:TEMP\vc_redist.x64.exe"
Invoke-WebRequest $vcRedistUrl -OutFile $vcFile
Start-Process $vcFile -ArgumentList "/install /quiet /norestart" -Wait

# PHP
$phpZip = "$env:TEMP\php.zip"
Invoke-WebRequest $phpZipUrl -OutFile $phpZip
Expand-Archive $phpZip -DestinationPath $phpFolder -Force
[Environment]::SetEnvironmentVariable("Path", $env:Path + ";$phpFolder", [EnvironmentVariableTarget]::Machine)

# Activar extensões PHP
$phpIni = "$phpFolder\php.ini"
Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
$needed = 'mbstring','openssl','simplexml','gd','fileinfo','curl','pdo_mysql','intl','zip'
(gc $phpIni) | % {
    if ($_ -match ";\s*extension=($($needed -join '|'))") {
        $_.TrimStart(';')
    } elseif ($_ -match "^;date.timezone") {
        "date.timezone = Europe/Lisbon"
    } elseif ($_ -match "^;upload_max_filesize") {
        "upload_max_filesize = 50M"
    } else {
        $_
    }
} | Set-Content $phpIni

# Composer
$composerInstaller = "$env:TEMP\Composer-Setup.exe"
Invoke-WebRequest $composerSetupUrl -OutFile $composerInstaller
Start-Process $composerInstaller -ArgumentList "/quiet" -Wait

# MariaDB
$mariadbMsi = "$env:TEMP\mariadb.msi"
Invoke-WebRequest "https://downloads.mariadb.org/f/mariadb-11.4.2/winx64-packages/mariadb-11.4.2-winx64.msi" -OutFile $mariadbMsi
Start-Process msiexec.exe -Wait -ArgumentList "/i `"$mariadbMsi`" /qn PASSWORD=$rootPassword SERVICENAME=MariaDB ADDLOCAL=Server,Client,SharedLibraries"

# Criar base de dados
$mysql = "${env:ProgramFiles}\MariaDB 11.4\bin\mysql.exe"
$sql = @"
CREATE DATABASE IF NOT EXISTS bookstack CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@
$sql | Out-File -Encoding ascii "$env:TEMP\create_bookstack.sql"
$script = Get-Content "$env:TEMP\create_bookstack.sql" -Raw
& $mysql -u root --password=$rootPassword -e "$script"

# BookStack clone
if (-not (Test-Path $bookstackFolder)) {
    git clone $bookstackRepo $bookstackFolder
}
cd $bookstackFolder
& composer install --no-dev

# .env
Copy-Item ".env.example" ".env" -Force
(gc ".env") | % {
    $_ -replace '^APP_URL=.*', 'APP_URL=https://localhost/bookstack' `
       -replace '^DB_HOST=.*', 'DB_HOST=127.0.0.1' `
       -replace '^DB_DATABASE=.*', 'DB_DATABASE=bookstack' `
       -replace '^DB_USERNAME=.*', 'DB_USERNAME=bookstack_user' `
       -replace '^DB_PASSWORD=.*', "DB_PASSWORD=$bookstackUserPassword"
} | Set-Content ".env"

# Chaves
php artisan key:generate
php artisan migrate --force

# IIS
Import-Module WebAdministration
New-WebAppPool -Name "BookStackAppPool"
New-Website -Name "BookStack" -Port 80 -PhysicalPath "$bookstackFolder\public" -ApplicationPool "BookStackAppPool"
New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
New-WebBinding -Name "BookStack" -Protocol https -Port 443 -HostHeader "localhost"
Set-WebConfigurationProperty -Filter '/system.webServer/security/access' -PSPath 'IIS:\Sites\BookStack' -Name 'sslFlags' -Value 'Ssl'

# Forçar HTTPS
$webconfig = "$bookstackFolder\public\web.config"
[xml]$xml = Get-Content $webconfig
$rewrite = $xml.configuration.'system.webServer'.rewrite
if (-not $rewrite) {
    $rewrite = $xml.CreateElement("rewrite")
    $xml.configuration.'system.webServer'.AppendChild($rewrite)
}
$rules = $rewrite.rules
$rule = $rules.AppendChild($xml.CreateElement("rule"))
$rule.SetAttribute("name", "Redirect to HTTPS")
$rule.SetAttribute("stopProcessing", "true")
$match = $rule.AppendChild($xml.CreateElement("match"))
$match.SetAttribute("url", ".*")
$conditions = $rule.AppendChild($xml.CreateElement("conditions"))
$add = $conditions.AppendChild($xml.CreateElement("add"))
$add.SetAttribute("input", "{HTTPS}")
$add.SetAttribute("pattern", "off")
$action = $rule.AppendChild($xml.CreateElement("action"))
$action.SetAttribute("type", "Redirect")
$action.SetAttribute("url", "https://{HTTP_HOST}/{R:1}")
$action.SetAttribute("redirectType", "Permanent")
$xml.Save($webconfig)

# Permissões
icacls "$bookstackFolder\storage" /grant "IIS_IUSRS:(OI)(CI)(M)" /T
icacls "$bookstackFolder\bootstrap\cache" /grant "IIS_IUSRS:(OI)(CI)(M)" /T

# Encerrar
Stop-Transcript

Write-Host "`n✅ Instalação concluída com sucesso!" -ForegroundColor Green
Write-Host "`n🔐 Credenciais criadas:"
Write-Host "  -> Utilizador base de dados: bookstack_user"
Write-Host "  -> Password: $bookstackUserPassword"
Write-Host "  -> Acede a: https://localhost/bookstack"
