# 📘 Script de Instalação Automatizada do BookStack (Windows + IIS)

Este script PowerShell (`Install-BookStack.ps1`) instala e configura automaticamente a aplicação **BookStack** num ambiente **Windows** com **IIS**, **MariaDB**, **PHP**, e **Composer**. É adequado para servidores Windows Server 2016+ ou Windows 10/11 com privilégios administrativos.

---

## ✅ Funcionalidade

Este script realiza os seguintes passos:

1. **Limpeza de instalações anteriores**:
   - Remove serviços MariaDB existentes
   - Desinstala pacotes via Chocolatey (MariaDB, PHP, Composer)
   - Remove pastas residuais

2. **Instalação de dependências**:
   - Instalação do [Chocolatey](https://chocolatey.org/) (se não existir)
   - Instalação do MariaDB (via Chocolatey)
   - Instalação do Composer e PHP com extensões necessárias
   - Instalação e configuração do IIS com suporte a HTTPS

3. **Configuração da base de dados MariaDB**:
   - Cria a base de dados `bookstack`
   - Cria o utilizador `bookstack_user` com permissões completas
   - Define a password do utilizador (`ZPH2LAB`)
   - Corre os comandos SQL para preparação inicial

4. **Instalação e configuração do BookStack**:
   - Clona o repositório BookStack a partir do GitHub
   - Instala dependências via Composer
   - Gera `.env` com base nos dados fornecidos
   - Gera a chave da aplicação Laravel
   - Corre as migrations da base de dados

5. **Configuração do IIS**:
   - Cria um site no IIS com ligação à pasta pública (`public/`)
   - Cria certificado **self-signed** para HTTPS local
   - Aplica redirecionamento de HTTP para HTTPS

6. **Credenciais iniciais**:
   - Utilizador: `<EMAIL>`
   - Password: `password`

---

## 💡 Requisitos

- Windows 10, 11 ou Windows Server 2016+ com privilégios de administrador
- Acesso à internet (para instalação de pacotes)
- PowerShell v5.1 ou superior

---

## 📦 Dependências Instaladas

- **MariaDB 11.8**
- **Composer** (via Chocolatey)
- **PHP 8.x** com extensões:
  - `mbstring`, `curl`, `gd`, `fileinfo`, `openssl`, `pdo`, `pdo_mysql`, `tokenizer`, `xml`, `ctype`, `bcmath`, `intl`

- **IIS** com:
  - Módulo CGI
  - Certificado HTTPS (autoassinado)
  - Redirecionamento de HTTP para HTTPS via `web.config`

---

## ⚠️ Notas de Segurança

- O script define uma password predefinida para a base de dados (`ZPH2LAB`) e para o utilizador BookStack. Esta **deve ser alterada após instalação**.
- O utilizador inicial do BookStack (`<EMAIL> / password`) também deve ser alterado logo após o primeiro login.

---

## 🚀 Como executar

1. **Abrir PowerShell como Administrador**
2. Correr o script:
   ```powershell
   Set-ExecutionPolicy Bypass -Scope Process -Force
   .\Install-BookStack.ps1
   ```

---

## 🌐 Acesso ao BookStack

Após instalação, acede a:

```
https://localhost
```

> Nota: Certificado é self-signed, por isso poderá dar aviso de segurança no browser.

---

## 📁 Estrutura Final

O BookStack ficará instalado por omissão em:

```
C:\inetpub\wwwroot\BookStack
```

A base de dados é criada localmente na instância MariaDB, na porta `3307`.

---

## 🧪 Verificação

O script inclui validações automáticas:

- Verifica se o serviço MariaDB está em execução
- Testa a ligação à base de dados
- Valida a criação do utilizador BookStack
- Confirma existência da aplicação Laravel (`artisan`, `.env`, etc.)

---

## 📌 Personalização

Para adaptar o script:

- **Alterar password da base de dados**: Procura a variável `$bookstackUserPassword`
- **Alterar domínio do IIS**: Modificar a configuração do site e certificado
- **Adicionar features extra**: Como LDAP, SMTP, etc., podes editar o `.env` após instalação

---

## 📎 Licença

Este script é fornecido como utilitário interno. Utiliza e adapta conforme as tuas necessidades.
