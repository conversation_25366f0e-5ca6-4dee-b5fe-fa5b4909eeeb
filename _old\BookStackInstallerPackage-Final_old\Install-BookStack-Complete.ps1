# Install-BookStack-Complete.ps1
# Script robusto para instalação segura do BookStack no Windows com IIS, MariaDB e HTTPS

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

function Stop-OnError($msg) {
    Write-Host "❌ $msg" -ForegroundColor Red
    Stop-Transcript
    exit 1
}

# 1. Pedir password do root MariaDB
$secureRootPassword = Read-Host "Introduz a password ROOT do MariaDB" -AsSecureString
$rootPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword)
)

# 2. Gerar password aleatória para o utilizador bookstack_user
Stop-Transcript
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | ForEach-Object {[char]$_})
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# 3. Instalar Chocolatey (se necessário)
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# 4. Instalar URL Rewrite
if (-not (Test-Path "$env:SystemRoot\System32\inetsrv\rewrite.dll")) {
    choco install urlrewrite -y || Stop-OnError "Falha ao instalar URL Rewrite"
}

# 5. Instalar Composer com .exe
$composerInstaller = "$env:TEMP\ComposerSetup.exe"
Invoke-WebRequest "https://getcomposer.org/Composer-Setup.exe" -OutFile $composerInstaller -ErrorAction Stop
Start-Process -FilePath $composerInstaller -ArgumentList "/quiet" -Wait
$composer = "$Env:ProgramFiles\ComposerSetup\bin\composer.bat"

# 6. Instalar PHP dinamicamente
$phpPage = Invoke-WebRequest "https://windows.php.net/downloads/releases/" -UseBasicParsing
$phpLink = ($phpPage.Links | Where-Object href -Match '-Win32-vs16-x64.zip$' | Sort-Object href -Descending)[0].href
$phpZipUrl = "https://windows.php.net$phpLink"
$phpZip = "$env:TEMP\php.zip"
$phpFolder = "C:\php"
Invoke-WebRequest $phpZipUrl -OutFile $phpZip -ErrorAction Stop
Expand-Archive $phpZip -DestinationPath $phpFolder -Force
$phpIni = "$phpFolder\php.ini"
Copy-Item "$phpFolder\php.ini-development" $phpIni -Force

# 7. Ativar extensões PHP
$needed = 'mbstring','openssl','simplexml','gd','fileinfo','curl','pdo_mysql','intl','zip'
(gc $phpIni) | % {
    if($_ -match ";\s*extension=($($needed -join '|'))") {
        $_.TrimStart(';')
    } elseif ($_ -match '^;?date\.timezone') {
        "date.timezone=Europe/Lisbon"
    } elseif ($_ -match '^;?upload_max_filesize') {
        "upload_max_filesize=50M"
    } else {
        $_
    }
} | Set-Content $phpIni

# 8. Adicionar PHP ao PATH
[Environment]::SetEnvironmentVariable("Path", "$phpFolder;$env:Path", "Machine")
$env:Path = "$phpFolder;$env:Path"

# 9. Instalar MariaDB
$mariadbInstaller = "$env:TEMP\mariadb.msi"
Invoke-WebRequest "https://downloads.mariadb.com/MariaDB/mariadb-11.4.2/winx64-packages/mariadb-11.4.2-winx64.msi" -OutFile $mariadbInstaller
Start-Process msiexec.exe -Wait -ArgumentList "/i `"$mariadbInstaller`" /qn PASSWORD=$rootPassword SERVICENAME=MariaDB ADDLOCAL=Server,Client,SharedLibraries,Node"

# 10. Localizar mysql.exe
$mysql = Get-Command mysql.exe -ErrorAction SilentlyContinue
if (-not $mysql) {
    $mysql = Get-ChildItem "C:\Program Files\MariaDB*" -Filter mysql.exe -Recurse | Select-Object -First 1 -ExpandProperty FullName
}
if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }

# 11. Criar base de dados e utilizador
$script = @"
CREATE DATABASE IF NOT EXISTS bookstack CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@
& $mysql -u root --password="$rootPassword" -e "$script" || Stop-OnError "Erro a criar base de dados."

# 12. Clonar BookStack
$bookstackFolder = "C:\inetpub\wwwroot\bookstack"
git clone https://github.com/BookStackApp/BookStack.git $bookstackFolder
cd $bookstackFolder
& "$composer" install --no-dev || Stop-OnError "Composer install falhou"

# 13. Criar .env
Copy-Item ".env.example" ".env" -Force
(Get-Content ".env") | ForEach-Object {
    $_ -replace '^APP_URL=.*', 'APP_URL=https://localhost/bookstack' `
       -replace '^DB_DATABASE=.*', 'DB_DATABASE=bookstack' `
       -replace '^DB_USERNAME=.*', 'DB_USERNAME=bookstack_user' `
       -replace '^DB_PASSWORD=.*', "DB_PASSWORD=$bookstackUserPassword"
} | Set-Content ".env"

# 14. Criar chave da aplicação e migrar BD
php artisan key:generate
php artisan migrate --force

# 15. Permissões
icacls "$bookstackFolder\storage" /grant "IIS_IUSRS:(OI)(CI)(M)" /T
icacls "$bookstackFolder\bootstrap\cache" /grant "IIS_IUSRS:(OI)(CI)(M)" /T
icacls "$bookstackFolder\storage\logs" /grant "IIS_IUSRS:(OI)(CI)(M)" /T

# 16. Criar site IIS
Import-Module WebAdministration
New-WebAppPool -Name "BookStackAppPool"
Set-ItemProperty IIS:\AppPools\BookStackAppPool -Name "managedRuntimeVersion" -Value ""
New-Website -Name "BookStack" -Port 80 -IPAddress "*" -PhysicalPath "$bookstackFolder\public" -ApplicationPool "BookStackAppPool"

# 17. HTTPS e certificado
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
New-Item -Path "IIS:\SslBindings\0.0.0.0!443" -Value $cert

# 18. Regra HTTPS + SSL no web.config
Add-Content "$bookstackFolder\public\web.config" @"
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name='Redirect to HTTPS' stopProcessing='true'>
          <match url='(.*)' />
          <conditions>
            <add input='{HTTPS}' pattern='off' ignoreCase='true' />
          </conditions>
          <action type='Redirect' url='https://{HTTP_HOST}/{R:1}' redirectType='Permanent' />
        </rule>
      </rules>
    </rewrite>
    <security>
      <access sslFlags="Ssl" />
    </security>
  </system.webServer>
</configuration>
"@

# 19. Fim
Stop-Transcript
Write-Host "`n✅ Instalação concluída com sucesso!"
Write-Host "`n🔐 Credenciais criadas:"
Write-Host "  -> Utilizador base de dados: bookstack_user"
Write-Host "  -> Password: $bookstackUserPassword"
Write-Host "  -> Acede a: https://localhost/bookstack"
