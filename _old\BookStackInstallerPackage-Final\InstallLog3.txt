**********************
PowerShell transcript start
Start time: 20250728220034
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 30800
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728220041
**********************
**********************
PowerShell transcript start
Start time: 20250728220041
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 30800
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Chocolatey v1.1.0
Installing the following packages:
vcredist140
By installing, you accept licenses for the packages.
vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - vcredist140 - vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
🔧 A instalar PHP…
WARNING: php.ini-development ausente; a criar php.ini vazio.
🔧 A instalar Composer…
🛠️  A instalar MariaDB (nova)…
Chocolatey v1.1.0
Installing the following packages:
mariadb
By installing, you accept licenses for the packages.
mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - mariadb - mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete2.ps1:111
Line |
 111 |  if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }
     |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ mysql.exe não encontrado.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete2.ps1:111
Line |
 111 |  if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }
     |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ mysql.exe não encontrado.

**********************
PowerShell transcript end
End time: 20250728220059
**********************
**********************
PowerShell transcript start
Start time: 20250728221118
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 30800
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728221122
**********************
**********************
PowerShell transcript start
Start time: 20250728221122
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 30800
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:68
Line |
  68 |  Copy-Item (Join-Path $phpRoot 'php.ini-production') $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php-8.3.23-devel-vs16-x64\php.ini-production' because it does not exist.
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:68
Line |
  68 |  Copy-Item (Join-Path $phpRoot 'php.ini-production') $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php-8.3.23-devel-vs16-x64\php.ini-production' because it does not exist.

Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:71
Line |
  71 |  (Get-Content $phpIni) |
     |   ~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php-8.3.23-devel-vs16-x64\php.ini' because it does not exist.
Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:71
Line |
  71 |  (Get-Content $phpIni) |
     |   ~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php-8.3.23-devel-vs16-x64\php.ini' because it does not exist.

🔧 A instalar Composer…
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> cls
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> .\Install-BookStack-Complete3.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:52
Line |
  52 |  … f (-not $phpRoot) { Stop-OnError "Falha ao localizar pasta do PHP." }
     |                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Falha ao localizar pasta do PHP.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:52
Line |
  52 |  … f (-not $phpRoot) { Stop-OnError "Falha ao localizar pasta do PHP." }
     |                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Falha ao localizar pasta do PHP.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> CLS
**********************
PowerShell transcript end
End time: 20250728221753
**********************
