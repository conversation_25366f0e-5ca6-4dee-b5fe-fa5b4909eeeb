**********************
PowerShell transcript start
Start time: 20250728215637
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 33996
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728215643
**********************
**********************
PowerShell transcript start
Start time: 20250728215643
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 33996
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Chocolatey v1.1.0
Installing the following packages:
vcredist140
By installing, you accept licenses for the packages.
vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - vcredist140 - vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
🔧 A instalar PHP…
WARNING: php.ini-development ausente; a criar php.ini vazio.
🔧 A instalar Composer…
🛠️  A instalar MariaDB (nova)…
Chocolatey v1.1.0
Installing the following packages:
mariadb
By installing, you accept licenses for the packages.
mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - mariadb - mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete2.ps1:111
Line |
 111 |  if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }
     |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ mysql.exe não encontrado.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete2.ps1:111
Line |
 111 |  if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }
     |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ mysql.exe não encontrado.

**********************
PowerShell transcript end
End time: 20250728215659
**********************
