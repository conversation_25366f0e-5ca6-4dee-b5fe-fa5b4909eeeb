
<#  Install‑BookStack‑Complete.ps1
    Instala BookStack + MariaDB + IIS (HTTPS) num Windows Server / Windows 10+
#>

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append
function Stop-OnError ($msg) { Write-Error "❌ $msg"; Stop-Transcript; exit 1 }

# 1. PASSWORDS
$secureRootPassword = Read-Host "Password ROOT actual do MariaDB (novo se for instalar)" -AsSecureString
$rootPassword       = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
                         [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword))
Stop-Transcript
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | % { [char]$_ })
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# 2. CHOCOLATEY & URL Rewrite
if (-not (Get-Command choco.exe -EA SilentlyContinue)) {
    Write-Host "📦 A instalar Chocolatey…"
    Set-ExecutionPolicy Bypass -Scope Process -Force
    iex ((New-Object Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}
if (-not (Test-Path "$env:SystemRoot\System32\inetsrv\rewrite.dll")) {
    choco install urlrewrite -y                || Stop-OnError "URL Rewrite falhou"
}
choco install vcredist140 -y --ignore-checksums | Out-Null

# 3. PHP
Write-Host "🔧 A instalar PHP…"
$phpPage = Invoke-WebRequest "https://windows.php.net/downloads/releases/" -UseBasicParsing
$phpLink = ($phpPage.Links |
            Where-Object { $_.href -match '-nts-Win32-vs16-x64\.zip$' -and $_.href -notmatch '(devel|debug)' } |
            Sort-Object href -Descending |
            Select-Object -First 1).href
if (-not $phpLink) { Stop-OnError "Nenhum build de release x64 encontrado." }
$phpZipUrl = "https://windows.php.net$phpLink"
$phpZip    = "$env:TEMP\php.zip"
Invoke-WebRequest $phpZipUrl -OutFile $phpZip -EA Stop

if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
New-Item C:\php -ItemType Directory | Out-Null
Expand-Archive $phpZip -DestinationPath C:\php -Force

$phpRoot = (Get-ChildItem C:\php -Recurse -Filter php.exe | Select-Object -First 1).Directory.FullName
if (-not $phpRoot) { Stop-OnError "Falha ao localizar php.exe." }

$phpIni  = Join-Path $phpRoot 'php.ini'
$iniProd = Join-Path $phpRoot 'php.ini-production'
$iniDev  = Join-Path $phpRoot 'php.ini-development'
if     (Test-Path $iniProd) { Copy-Item $iniProd $phpIni -Force }
elseif (Test-Path $iniDev)  { Copy-Item $iniDev  $phpIni -Force }
else   { New-Item $phpIni -ItemType File | Out-Null }

$ext = 'mbstring','openssl','simplexml','gd','fileinfo','curl','pdo_mysql','intl','zip'
(Get-Content $phpIni) |
  ForEach-Object {
      if    ($_ -match "^\s*;\s*extension=($($ext -join '|'))") { $_.TrimStart(';') }
      elseif($_ -match '^;?date\.timezone')     { 'date.timezone = Europe/Lisbon' }
      elseif($_ -match '^;?upload_max_filesize') { 'upload_max_filesize = 50M' }
      else { $_ }
  } | Set-Content $phpIni

[Environment]::SetEnvironmentVariable('Path', "$phpRoot;$env:Path", 'Machine')
$env:Path = "$phpRoot;$env:Path"

# 4. COMPOSER
Write-Host "🔧 A instalar Composer…"
$composerSetup = "$env:TEMP\ComposerSetup-$PID.exe"
Invoke-WebRequest https://getcomposer.org/Composer-Setup.exe -OutFile $composerSetup -EA Stop
Start-Process $composerSetup -ArgumentList '/SILENT', '/ALLUSERS=1', '/NORESTART' -Wait

$composer = Get-Command composer -EA SilentlyContinue |
            Select-Object -First 1 -ExpandProperty Source
if (-not $composer) {
    $composer = Get-ChildItem "$Env:ProgramData\ComposerSetup","$Env:AppData\ComposerSetup" -Recurse -Filter composer.* |
                Where-Object { $_.Name -match '^composer\.(bat|cmd)$' } |
                Select-Object -First 1 -Expand FullName
}
if (-not $composer) { Stop-OnError "Composer não ficou acessível." }

# 5. MARIADB
function Test-RootLogin ([string]$Pwd) {
    $mysqlCmd = Get-Command mysql.exe -EA SilentlyContinue | Select -First 1 -Expand Source
    if ($mysqlCmd) { & $mysqlCmd -u root --password="$Pwd" -e "SELECT 1;" 2>$null }
    return $LASTEXITCODE -eq 0
}

Write-Host "🗑️ Limpeza completa do MariaDB..."

# Parar serviços
Get-Service -Name "*MariaDB*" -ErrorAction SilentlyContinue | Stop-Service -Force -ErrorAction SilentlyContinue

# Remover serviços
$services = Get-WmiObject -Class Win32_Service | Where-Object { $_.Name -like "*MariaDB*" }
foreach ($service in $services) {
    Write-Host "Removendo serviço: $($service.Name)"
    & sc.exe delete $service.Name
}

# Forçar remoção dos pacotes Chocolatey
choco uninstall mariadb mariadb.install -y --force --remove-dependencies --skip-autouninstaller -n

# Remover pastas manualmente
$foldersToRemove = @(
    "C:\Program Files\MariaDB*",
    "C:\ProgramData\MySQL",
    "C:\ProgramData\MariaDB",
    "$Env:ProgramData\chocolatey\lib\mariadb*"
)

foreach ($folder in $foldersToRemove) {
    Get-ChildItem $folder -ErrorAction SilentlyContinue | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "📦 Instalação limpa do MariaDB..."
choco install mariadb.install -y --force --params "/rootpwd=`"$rootPassword`"" 
if ($LASTEXITCODE -ne 0) { Stop-OnError "Falha na instalação do MariaDB" }
Start-Sleep -Seconds 20

# Verificar se os binários foram instalados
$binPath = "C:\Program Files\MariaDB*\bin"
$binFolder = Get-ChildItem $binPath -ErrorAction SilentlyContinue | Select-Object -First 1
if ($binFolder) {
    Write-Host "✅ Binários encontrados em: $($binFolder.FullName)"
    $mysqlExe = Join-Path $binFolder.FullName "mysql.exe"
    if (Test-Path $mysqlExe) {
        Write-Host "✅ mysql.exe encontrado!"
        $env:PATH = "$($binFolder.FullName);$env:PATH"
    } else {
        Stop-OnError "mysql.exe não encontrado na pasta bin"
    }
} else {
    Stop-OnError "Pasta bin do MariaDB não encontrada após instalação"
}

# Verificar e iniciar serviço
$svc = Get-Service -Name "*MariaDB*" -ErrorAction SilentlyContinue | Select-Object -First 1
if ($null -eq $svc) { 
    Write-Host "⚠️ Serviço MariaDB não encontrado. Tentando registar..."
    $mysqldExe = Join-Path $binFolder.FullName "mysqld.exe"
    if (Test-Path $mysqldExe) {
        & $mysqldExe --install MariaDB
        Start-Sleep -Seconds 5
        $svc = Get-Service -Name MariaDB -ErrorAction SilentlyContinue
    }
    if ($null -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB." }
}

Write-Host "� Serviço encontrado: $($svc.Name) - Status: $($svc.Status)"

if ($svc.Status -ne 'Running') {
    Write-Host "🔄 Iniciando serviço MariaDB..."
    Start-Service -Name $svc.Name -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 10
    
    # Verificar se iniciou
    $svc.Refresh()
    if ($svc.Status -ne 'Running') {
        Write-Host "⚠️ Serviço não iniciou. Tentando inicializar dados..."
        $mysqldExe = Join-Path $binFolder.FullName "mysqld.exe"
        $dataDir = Join-Path (Split-Path $binFolder.FullName) "data"
        
        if (-not (Test-Path $dataDir)) {
            New-Item -ItemType Directory -Path $dataDir -Force
        }
        
        # Inicializar base de dados
        & $mysqldExe --initialize-insecure --datadir="$dataDir"
        Start-Sleep -Seconds 5
        
        # Tentar iniciar novamente
        Start-Service -Name $svc.Name
        Start-Sleep -Seconds 10
    }
}

# Localizar mysql.exe com busca abrangente
Write-Host "🔍 Localizando mysql.exe..."
$mysql = Get-Command mysql.exe -ErrorAction SilentlyContinue | Select-Object -First 1 -ExpandProperty Source

if (-not $mysql) {
    $mysql = Get-ChildItem "C:\Program Files\MariaDB*" -Recurse -Filter mysql.exe -ErrorAction SilentlyContinue |
             Select-Object -First 1 -ExpandProperty FullName
    if ($mysql) {
        $mysqlDir = Split-Path $mysql
        $env:PATH = "$mysqlDir;$env:PATH"
    }
}

if (-not $mysql) {
    $mysql = Get-ChildItem "$Env:ProgramData\chocolatey\lib\mariadb*" -Recurse -Filter mysql.exe -ErrorAction SilentlyContinue |
             Select-Object -First 1 -ExpandProperty FullName
    if ($mysql) {
        $mysqlDir = Split-Path $mysql
        $env:PATH = "$mysqlDir;$env:PATH"
    }
}

if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }

# Testar conexão e configurar password
Write-Host "🔑 Testando conexão com password fornecida..."
$testResult = & $mysql -u root --password="$rootPassword" -e "SELECT 1 AS test;" 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Conexão bem-sucedida com a password fornecida!"
} else {
    Write-Host "⚠️ Erro na conexão. Tentando acesso sem password..."
    
    $testNoPass = & $mysql -u root -e "SELECT 1 AS test;" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "🔄 Acesso sem password detectado. Definindo nova password..."
        $setPassResult = & $mysql -u root -e "SET PASSWORD FOR 'root'@'localhost' = PASSWORD('$rootPassword'); FLUSH PRIVILEGES;" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Password root configurada com sucesso!"
        } else {
            Stop-OnError "Falha ao configurar password root"
        }
    } else {
        Stop-OnError "Não foi possível aceder ao MariaDB"
    }
}

# Configurar base de dados BookStack
$sql = @"
CREATE DATABASE IF NOT EXISTS bookstack CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@
& $mysql -u root --password="$rootPassword" -e "$sql" || Stop-OnError "Configuração da BD falhou."

# 6. BOOKSTACK
$bookstackDir = "C:\inetpub\wwwroot\bookstack"
if (Test-Path $bookstackDir) { Remove-Item $bookstackDir -Recurse -Force }
git clone https://github.com/BookStackApp/BookStack.git $bookstackDir || Stop-OnError "Clone BookStack falhou."

Set-Location $bookstackDir
& $composer install --no-dev --no-interaction || Stop-OnError "Composer falhou."

Copy-Item .env.example .env -Force
(Get-Content .env) |
  ForEach-Object {
      $_ -replace '^APP_URL=.*',    'APP_URL=https://localhost/bookstack' `
         -replace '^DB_DATABASE=.*','DB_DATABASE=bookstack' `
         -replace '^DB_USERNAME=.*','DB_USERNAME=bookstack_user' `
         -replace '^DB_PASSWORD=.*',"DB_PASSWORD=$bookstackUserPassword"
  } | Set-Content .env

# DIAGNÓSTICO COMPLETO
Write-Host "🔍 TESTE DE CONEXÃO À BASE DE DADOS:"
Write-Host "===================================="

# Mostrar configuração
Write-Host "📄 Configuração .env:"
Get-Content .env | Where-Object { $_ -match "^(DB_|APP_URL)" } | ForEach-Object { Write-Host "   $_" }

# Teste MySQL direto
Write-Host "`n🔌 Teste MySQL direto:"
$mysqlTest = & $mysql -u bookstack_user --password="$bookstackUserPassword" -D bookstack -e "SELECT 'MySQL OK' AS test;" 2>&1
Write-Host "   $mysqlTest"

# Teste PHP
Write-Host "`n🐘 Teste PHP PDO:"
$phpTest = php -r "try { `$pdo = new PDO('mysql:host=localhost;dbname=bookstack;charset=utf8mb4', 'bookstack_user', '$bookstackUserPassword'); echo 'PHP OK'; } catch(Exception `$e) { echo 'ERRO: ' . `$e->getMessage(); exit(1); }" 2>&1
Write-Host "   $phpTest"

if ($LASTEXITCODE -ne 0) {
    Write-Host "`n🔧 Recriando utilizador..."
    $simpleSql = @"
DROP USER IF EXISTS 'bookstack_user'@'localhost';
CREATE USER 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@
    & $mysql -u root --password="$rootPassword" -e "$simpleSql"
    
    # Teste final
    $finalTest = php -r "try { `$pdo = new PDO('mysql:host=localhost;dbname=bookstack;charset=utf8mb4', 'bookstack_user', '$bookstackUserPassword'); echo 'OK'; } catch(Exception `$e) { echo 'ERRO'; exit(1); }" 2>&1
    if ($LASTEXITCODE -ne 0) { Stop-OnError "Conexão PHP impossível" }
}

Write-Host "`n✅ Conexão verificada!"

php artisan key:generate       || Stop-OnError "artisan key:generate falhou."
php artisan migrate --force    || Stop-OnError "artisan migrate falhou."

# 7. PERMISSÕES
icacls "$bookstackDir\storage"          /grant 'IIS_IUSRS:(OI)(CI)(M)' /T
icacls "$bookstackDir\bootstrap\cache" /grant 'IIS_IUSRS:(OI)(CI)(M)' /T

# 8. IIS + HTTPS
Import-Module WebAdministration -SkipEditionCheck

# Encontrar porta disponível
$httpPort = 8080
$httpsPort = 8443
while ((Get-NetTCPConnection -LocalPort $httpPort -EA SilentlyContinue) -or (Get-Website | Where-Object { $_.bindings.Collection.bindingInformation -like "*:${httpPort}:*" })) {
    $httpPort++
}
while ((Get-NetTCPConnection -LocalPort $httpsPort -EA SilentlyContinue) -or (Get-Website | Where-Object { $_.bindings.Collection.bindingInformation -like "*:${httpsPort}:*" })) {
    $httpsPort++
}

Write-Host "🌐 Usando portas: HTTP=$httpPort, HTTPS=$httpsPort"

# Atualizar .env com a porta correta
(Get-Content "$bookstackDir\.env") |
  ForEach-Object {
      $_ -replace '^APP_URL=.*', "APP_URL=https://localhost:$httpsPort/bookstack"
  } | Set-Content "$bookstackDir\.env"

if (-not (Get-WebAppPoolState 'BookStackAppPool' -EA SilentlyContinue)) {
    New-WebAppPool 'BookStackAppPool'
    Set-ItemProperty "IIS:\AppPools\BookStackAppPool" managedRuntimeVersion ''
}

# Remover site existente se existir
if (Get-Website 'BookStack' -EA SilentlyContinue) {
    Remove-Website 'BookStack'
}

# Criar site com portas disponíveis
New-Website -Name 'BookStack' -Port $httpPort -PhysicalPath "$bookstackDir\public" -ApplicationPool 'BookStackAppPool'

$cert = Get-ChildItem Cert:\LocalMachine\My | ? { $_.Subject -eq 'CN=localhost' } | Select -First 1
if (-not $cert) { $cert = New-SelfSignedCertificate -DnsName localhost -CertStoreLocation Cert:\LocalMachine\My }

# Adicionar binding HTTPS
New-WebBinding -Name BookStack -Protocol https -Port $httpsPort

# Configurar SSL
try {
    $binding = Get-WebBinding -Name BookStack -Protocol https -Port $httpsPort
    $binding.AddSslCertificate($cert.Thumbprint, "my")
} catch {
    Write-Host "⚠️ Configurar SSL manualmente no IIS Manager"
}

# 9. DONE
Stop-Transcript
Write-Host "`n✅ Instalação concluída com sucesso!"
Write-Host "`n🔐 Credenciais:"
Write-Host "   ➤ Utilizador BD : bookstack_user"
Write-Host "   ➤ Password BD   : $bookstackUserPassword"
Write-Host "   ➤ URL HTTP      : http://localhost:$httpPort"
Write-Host "   ➤ URL HTTPS     : https://localhost:$httpsPort"
Write-Host "`n📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager`n"






