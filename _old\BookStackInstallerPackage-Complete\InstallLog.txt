**********************
PowerShell transcript start
Start time: 20250728194009
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 3856
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728194015
**********************
**********************
PowerShell transcript start
Start time: 20250728194015
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 3856
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\InstallLog.txt
Chocolatey v1.1.0
Installing the following packages:
urlrewrite
By installing, you accept licenses for the packages.
Progress: Downloading UrlRewrite 2.1.20190828... 100%

UrlRewrite v2.1.20190828 [Approved]
urlrewrite package files install completed. Performing other installation steps.
Downloading UrlRewrite 64 bit
  from 'https://download.microsoft.com/download/1/2/8/128E2E22-C1B9-44A4-BE2A-5859ED1D4592/rewrite_amd64_en-US.msi'
Progress: 100% - Completed download of C:\Users\<USER>\AppData\Local\Temp\chocolatey\UrlRewrite\2.1.20190828\rewrite_
amd64_en-US.msi (5.8 MB).
Download of rewrite_amd64_en-US.msi (5.8 MB) completed.
Hashes match.
Installing UrlRewrite...
UrlRewrite has been installed.
  urlrewrite may be able to be automatically uninstalled.
 The install of urlrewrite was successful.
  Software installed as 'MSI', install location is likely default.

Chocolatey installed 1/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Enjoy using Chocolatey? Explore more amazing features to take your
experience to the next level at
 https://chocolatey.org/compare
PS>TerminatingError(Invoke-WebRequest): "



404 - File or directory not found.





Server Error

 
  404 - File or directory not found.
  The resource you are looking for might have been removed, had its name changed, or is temporarily unavailable.
 



"
Invoke-WebRequest: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:54
Line |
  54 |  Invoke-WebRequest $phpZipUrl -OutFile $phpZip
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     |     404 - File or directory not found.      Server Error      404 - File or directory not found.   The resource you are looking for might have been removed, had its name changed, or is temporarily unavailable.
Invoke-WebRequest: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:54
Line |
  54 |  Invoke-WebRequest $phpZipUrl -OutFile $phpZip
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     |     404 - File or directory not found.      Server Error      404 - File or directory not found.   The resource
     | you are looking for might have been removed, had its name changed, or is temporarily unavailable.

PS>TerminatingError(Resolve-Path): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Cannot find path 'C:\Users\<USER>\AppData\Local\Temp\php.zip' because it does not exist."
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(Expand-Archive): "The path 'C:\Users\<USER>\AppData\Local\Temp\php.zip' either does not exist or is not a valid file system path."
Expand-Archive: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:55
Line |
  55 |  Expand-Archive $phpZip -DestinationPath $phpFolder -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The path 'C:\Users\<USER>\AppData\Local\Temp\php.zip' either does not exist or is not a valid file system path.
Expand-Archive: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:55
Line |
  55 |  Expand-Archive $phpZip -DestinationPath $phpFolder -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The path 'C:\Users\<USER>\AppData\Local\Temp\php.zip' either does not exist or is not a valid file system path.

Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:60
Line |
  60 |  Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini-development' because it does not exist.
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:60
Line |
  60 |  Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini-development' because it does not exist.

Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:62
Line |
  62 |  (gc $phpIni) | % {
     |   ~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini' because it does not exist.
Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:62
Line |
  62 |  (gc $phpIni) | % {
     |   ~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini' because it does not exist.

&: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:94
Line |
  94 |  & $mysql -u root --password=$rootPassword -e "$script"
     |    ~~~~~~
     | The term 'C:\Program Files\MariaDB 11.4\bin\mysql.exe' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
&: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:94
Line |
  94 |  & $mysql -u root --password=$rootPassword -e "$script"
     |    ~~~~~~
     | The term 'C:\Program Files\MariaDB 11.4\bin\mysql.exe' is not recognized as a name of a cmdlet, function, script
     | file, or executable program. Check the spelling of the name, or if a path was included, verify that the path is
     | correct and try again.

Cloning into 'C:\inetpub\wwwroot\bookstack'...nsion is in progress...                                                ]
remote: Enumerating objects: 72028, done.
remote: Counting objects: 100% (592/592), done.
remote: Compressing objects: 100% (259/259), done.
remote: Total 72028 (delta 426), reused 335 (delta 333), pack-reused 71436 (from 2)
Receiving objects: 100% (72028/72028), 45.22 MiB | 27.32 MiB/s, done.
Resolving deltas: 100% (53184/53184), done.
&: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:101
Line |
 101 |  & composer install --no-dev
     |    ~~~~~~~~
     | The term 'composer' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
&: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:101
Line |
 101 |  & composer install --no-dev
     |    ~~~~~~~~
     | The term 'composer' is not recognized as a name of a cmdlet, function, script file, or executable program. Check
     | the spelling of the name, or if a path was included, verify that the path is correct and try again.

php: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:114
Line |
 114 |  php artisan key:generate
     |  ~~~
     | The term 'php' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
php: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:114
Line |
 114 |  php artisan key:generate
     |  ~~~
     | The term 'php' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the
     | spelling of the name, or if a path was included, verify that the path is correct and try again.

php: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:115
Line |
 115 |  php artisan migrate --force
     |  ~~~
     | The term 'php' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
php: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Complete\Install-BookStack-Complete.ps1:115
Line |
 115 |  php artisan migrate --force
     |  ~~~
     | The term 'php' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the
     | spelling of the name, or if a path was included, verify that the path is correct and try again.

WARNING: Module WebAdministration is loaded in Windows PowerShell using WinPSCompatSession remoting session; please note that all input and output of commands from this module will be deserialized objects. If you want to load this module into PowerShell please use 'Import-Module -SkipEditionCheck' syntax.

Name                     State        Applications
----                     -----        ------------
BookStackAppPool

name                       : BookStack
id                         : 5
serverAutoStart            : True
state                      : Stopped
bindings                   : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
limits                     : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
logFile                    : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
traceFailedRequestsLogging : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
hsts                       : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
applicationDefaults        : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
virtualDirectoryDefaults   : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
ftpServer                  : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
Collection                 : {Microsoft.IIs.PowerShell.Framework.ConfigurationElement}
applicationPool            : BookStackAppPool
enabledProtocols           : http
physicalPath               : C:\inetpub\wwwroot\bookstack\public
userName                   : 
password                   : 
ItemXPath                  : /system.applicationHost/sites/site[@name='BookStack' and @id='5']
PSPath                     : WebAdministration::\\PROCYON\Sites\BookStack
PSParentPath               : WebAdministration::\\PROCYON\Sites
PSChildName                : BookStack
PSProvider                 : WebAdministration
PSIsContainer              : True
RunspaceId                 : 4e956a24-ff17-4e6a-8ac0-5bbcbc2976d1
Attributes                 : {Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute}
ChildElements              : {Microsoft.IIs.PowerShell.Framework.ConfigurationElement,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationElement,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationElement,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationElement…}
ElementTagName             : site
Methods                    : {Microsoft.IIs.PowerShell.Framework.ConfigurationMethod,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationMethod}
Schema                     : Microsoft.IIs.PowerShell.Framework.ConfigurationElementSchema


PSPath                   : Microsoft.PowerShell.Security\Certificate::LocalMachine\My\CCCFB1808FCEADC66C1F3AF33BA6DBD3E
                           5820B4C
PSParentPath             : Microsoft.PowerShell.Security\Certificate::LocalMachine\My
PSChildName              : CCCFB1808FCEADC66C1F3AF33BA6DBD3E5820B4C
PSIsContainer            : False
Archived                 : False
Extensions               : {System.Security.Cryptography.Oid, System.Security.Cryptography.Oid,
                           System.Security.Cryptography.Oid, System.Security.Cryptography.Oid}
FriendlyName             : 
HasPrivateKey            : True
PrivateKey               : System.Security.Cryptography.RSACng
IssuerName               : System.Security.Cryptography.X509Certificates.X500DistinguishedName
NotAfter                 : 28/07/2026 20:24:53
NotBefore                : 28/07/2025 20:04:53
PublicKey                : System.Security.Cryptography.X509Certificates.PublicKey
RawData                  : {48, 130, 3, 24…}
RawDataMemory            : System.ReadOnlyMemory<Byte>[796]
SerialNumber             : 54F6C3C13D4484B64FED24BB9F33ABC9
SignatureAlgorithm       : System.Security.Cryptography.Oid
SubjectName              : System.Security.Cryptography.X509Certificates.X500DistinguishedName
Thumbprint               : CCCFB1808FCEADC66C1F3AF33BA6DBD3E5820B4C
Version                  : 3
Handle                   : 3025870271936
Issuer                   : CN=localhost
Subject                  : CN=localhost
SerialNumberBytes        : System.ReadOnlyMemory<Byte>[16]
EnhancedKeyUsageList     : {Client Authentication (*******.*******.2), Server Authentication (*******.*******.1)}
DnsNameList              : {localhost}
SendAsTrustedIssuer      : False
EnrollmentPolicyEndPoint : Microsoft.CertificateServices.Commands.EnrollmentEndPointProperty
EnrollmentServerEndPoint : Microsoft.CertificateServices.Commands.EnrollmentEndPointProperty
PolicyId                 : 

Set-WebConfigurationProperty: This configuration section cannot be used at this path. This happens when the section is locked at a parent level. Locking is either by default (overrideModeDefault="Deny"), or set explicitly by a location tag with overrideMode="Deny" or the legacy allowOverride="false".
Set-WebConfigurationProperty: This configuration section cannot be used at this path. This happens when the section is locked at a parent level. Locking is either by default (overrideModeDefault="Deny"), or set explicitly by a location tag with overrideMode="Deny" or the legacy allowOverride="false".

processed file: C:\inetpub\wwwroot\bookstack\storageis in progress...                                                ]
processed file: C:\inetpub\wwwroot\bookstack\storage\app
processed file: C:\inetpub\wwwroot\bookstack\storage\backups
processed file: C:\inetpub\wwwroot\bookstack\storage\clockwork
processed file: C:\inetpub\wwwroot\bookstack\storage\fonts
processed file: C:\inetpub\wwwroot\bookstack\storage\framework
processed file: C:\inetpub\wwwroot\bookstack\storage\logs
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads
processed file: C:\inetpub\wwwroot\bookstack\storage\app\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\backups\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\clockwork\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\fonts\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\cache
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\sessions
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\views
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\cache\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\sessions\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\views\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\logs\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads\files
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads\images
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads\files\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads\images\.gitignore
Successfully processed 24 files; Failed processing 0 files
processed file: C:\inetpub\wwwroot\bookstack\bootstrap\cache
processed file: C:\inetpub\wwwroot\bookstack\bootstrap\cache\.gitignore
Successfully processed 2 files; Failed processing 0 files
**********************
PowerShell transcript end
End time: 20250728201454
**********************
