# BookStack Installer para Windows Server (IIS + MariaDB + HTTPS)

Este pacote contém tudo o que precisas para instalar o BookStack de forma segura e automatizada:

## Conteúdo

- `Install-BookStack-Final.ps1` — Script PowerShell de instalação.
- `.env.template` — Template de configuração da aplicação.
- `README.md` — Instruções de utilização.

## Como usar

1. Abre uma consola PowerShell como **Administrador**.
2. Corre o script principal:

```powershell
.\Install-BookStack-Final.ps1
```

3. Será-te pedida a password do utilizador `root` da base de dados MariaDB.
4. O script irá gerar automaticamente a password do utilizador `bookstack_user`.
5. No final, acede a:

```
https://localhost/bookstack
```

(será usado um certificado **self-signed** criado pelo script)
