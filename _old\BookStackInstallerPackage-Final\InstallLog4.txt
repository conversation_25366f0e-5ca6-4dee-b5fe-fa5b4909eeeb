**********************
PowerShell transcript start
Start time: 20250728222058
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 13400
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728222102
**********************
**********************
PowerShell transcript start
Start time: 20250728222102
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 13400
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:76
Line |
  76 |  … est-Path $composer)) { Stop-OnError "Composer não ficou acessível." }
     |                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Composer não ficou acessível.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:76
Line |
  76 |  … est-Path $composer)) { Stop-OnError "Composer não ficou acessível." }
     |                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Composer não ficou acessível.

**********************
PowerShell transcript end
End time: 20250728222119
**********************
**********************
PowerShell transcript start
Start time: 20250728222353
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 13400
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728222358
**********************
**********************
PowerShell transcript start
Start time: 20250728222358
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 13400
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🛠️  A instalar MariaDB (nova)…
Chocolatey v1.1.0
Installing the following packages:
mariadb
By installing, you accept licenses for the packages.
Progress: Downloading mariadb 11.8.2... 100%

mariadb v11.8.2 [Approved]
mariadb package files install completed. Performing other installation steps.
 The install of mariadb was successful.
  Software installed to 'C:\ProgramData\chocolatey\lib\mariadb'

Chocolatey installed 1/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:110
Line |
 110 |  if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }
     |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ mysql.exe não encontrado.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:110
Line |
 110 |  if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }
     |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ mysql.exe não encontrado.

**********************
PowerShell transcript end
End time: 20250728222408
**********************
**********************
PowerShell transcript start
Start time: 20250728222619
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 13400
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728222624
**********************
**********************
PowerShell transcript start
Start time: 20250728222624
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 13400
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🛠️  A instalar MariaDB (nova)…
Chocolatey v1.1.0
Installing the following packages:
mariadb.install
By installing, you accept licenses for the packages.
mariadb.install v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - mariadb.install - mariadb.install v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:110
Line |
 110 |  if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }
     |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ mysql.exe não encontrado.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:110
Line |
 110 |  if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }
     |                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ mysql.exe não encontrado.

**********************
PowerShell transcript end
End time: 20250728222634
**********************
**********************
PowerShell transcript start
Start time: 20250728225716
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 26964
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728225722
**********************
**********************
PowerShell transcript start
Start time: 20250728225722
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 26964
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🛠️  A instalar/actualizar MariaDB…
Chocolatey v1.1.0
Upgrading the following packages:
mariadb.install
By upgrading, you accept licenses for the packages.
mariadb.install v11.8.2 is the latest version available based on your source(s).

mariadb.install v11.8.2 (forced) [Approved]
mariadb.install package files upgrade completed. Performing other installation steps.
Installing 64-bit mariadb.install...
mariadb.install has been installed.
The requested service has already been started.

More help is available by typing NET HELPMSG 2182.

  mariadb.install may be able to be automatically uninstalled.
 The upgrade of mariadb.install was successful.
  Software installed to 'C:\Program Files\MariaDB 11.8\'

Chocolatey upgraded 1/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:120
Line |
 120 |  … $rootPassword" -e "$sql" || Stop-OnError "Configuração da BD falhou."
     |                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Configuração da BD falhou.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:120
Line |
 120 |  … $rootPassword" -e "$sql" || Stop-OnError "Configuração da BD falhou."
     |                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Configuração da BD falhou.

**********************
PowerShell transcript end
End time: 20250728225753
**********************
**********************
PowerShell transcript start
Start time: 20250728230845
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 29044
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728230851
**********************
**********************
PowerShell transcript start
Start time: 20250728230851
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 29044
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🛠️  A instalar/actualizar MariaDB…
Chocolatey v1.1.0
Upgrading the following packages:
mariadb.install
By upgrading, you accept licenses for the packages.
mariadb.install v11.8.2 is the latest version available based on your source(s).
Progress: Downloading mariadb.install 11.8.2... 100%

mariadb.install v11.8.2 (forced) [Approved]
mariadb.install package files upgrade completed. Performing other installation steps.
Installing 64-bit mariadb.install...
mariadb.install has been installed.
The requested service has already been started.

More help is available by typing NET HELPMSG 2182.

  mariadb.install may be able to be automatically uninstalled.
 The upgrade of mariadb.install was successful.
  Software installed to 'C:\Program Files\MariaDB 11.8\'

Chocolatey upgraded 1/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:129
Line |
 129 |  … $rootPassword" -e "$sql" || Stop-OnError "Configuração da BD falhou."
     |                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Configuração da BD falhou.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete3.ps1:129
Line |
 129 |  … $rootPassword" -e "$sql" || Stop-OnError "Configuração da BD falhou."
     |                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Configuração da BD falhou.

**********************
PowerShell transcript end
End time: 20250728230920
**********************
