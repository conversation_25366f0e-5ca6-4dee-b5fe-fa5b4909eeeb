<#
.SYNOPSIS
    Script simples para instalação limpa do MariaDB no Windows.
.DESCRIPTION
    Instala MariaDB via Chocolatey em sistemas Windows, configura o serviço,
    inicializa o diretório de dados e define a password root fornecida pelo utilizador.
#>

param ()

function Stop-OnError($msg) {
    Write-Error "❌ $msg"
    exit 1
}

# 1. Pedir password ROOT
$secureRootPassword = Read-Host -Prompt "Introduz a password ROOT do MariaDB" -AsSecureString
$rootPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword)
)

# 2. Instalar Chocolatey se necessário
if (-not (Get-Command choco.exe -ErrorAction SilentlyContinue)) {
    Write-Host "📥 Instalando Chocolatey..."
    Set-ExecutionPolicy Bypass -Scope Process -Force
    iex ((New-Object Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    # Verificar se foi instalado
    if (-not (Get-Command choco.exe -ErrorAction SilentlyContinue)) {
        Stop-OnError "Chocolatey não foi instalado corretamente"
    }
}

# 3. Limpeza completa manual
Write-Host "🗑️ Limpeza completa do MariaDB..."

# Parar serviços
Get-Service -Name "*MariaDB*" -ErrorAction SilentlyContinue | Stop-Service -Force -ErrorAction SilentlyContinue

# Remover serviços
$services = Get-WmiObject -Class Win32_Service | Where-Object { $_.Name -like "*MariaDB*" }
foreach ($service in $services) {
    Write-Host "Removendo serviço: $($service.Name)"
    & sc.exe delete $service.Name
}

# Forçar remoção dos pacotes Chocolatey
choco uninstall mariadb mariadb.install -y --force --remove-dependencies --skip-autouninstaller -n

# Remover pastas manualmente
$foldersToRemove = @(
    "C:\Program Files\MariaDB*",
    "C:\ProgramData\MySQL",
    "C:\ProgramData\MariaDB",
    "$Env:ProgramData\chocolatey\lib\mariadb*"
)

foreach ($folder in $foldersToRemove) {
    Get-ChildItem $folder -ErrorAction SilentlyContinue | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "📦 Instalação limpa do MariaDB..."
choco install mariadb.install -y --force --params "/rootpwd=`"$rootPassword`"" 
if ($LASTEXITCODE -ne 0) { Stop-OnError "Falha na instalação do MariaDB" }
Start-Sleep -Seconds 20

# Verificar se os binários foram instalados
$binPath = "C:\Program Files\MariaDB*\bin"
$binFolder = Get-ChildItem $binPath -ErrorAction SilentlyContinue | Select-Object -First 1
if ($binFolder) {
    Write-Host "✅ Binários encontrados em: $($binFolder.FullName)"
    $mysqlExe = Join-Path $binFolder.FullName "mysql.exe"
    if (Test-Path $mysqlExe) {
        Write-Host "✅ mysql.exe encontrado!"
        $env:PATH = "$($binFolder.FullName);$env:PATH"
    } else {
        Write-Host "❌ mysql.exe não encontrado na pasta bin"
        Get-ChildItem $binFolder.FullName | Select-Object Name | Format-Table -AutoSize
    }
} else {
    Stop-OnError "Pasta bin do MariaDB não encontrada após instalação"
}

# 4. Verificar e iniciar serviço
$svc = Get-Service -Name "*MariaDB*" -ErrorAction SilentlyContinue | Select-Object -First 1
if ($null -eq $svc) { 
    Write-Host "⚠️ Serviço MariaDB não encontrado. Tentando registar..."
    $mysqldExe = Join-Path $binFolder.FullName "mysqld.exe"
    if (Test-Path $mysqldExe) {
        & $mysqldExe --install MariaDB
        Start-Sleep -Seconds 5
        $svc = Get-Service -Name MariaDB -ErrorAction SilentlyContinue
    }
    if ($null -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB." }
}

Write-Host "🔍 Serviço encontrado: $($svc.Name) - Status: $($svc.Status)"

if ($svc.Status -ne 'Running') {
    Write-Host "🔄 Iniciando serviço MariaDB..."
    Start-Service -Name $svc.Name -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 10
    
    # Verificar se iniciou
    $svc.Refresh()
    if ($svc.Status -ne 'Running') {
        Write-Host "⚠️ Serviço não iniciou. Tentando inicializar dados..."
        $mysqldExe = Join-Path $binFolder.FullName "mysqld.exe"
        $dataDir = Join-Path (Split-Path $binFolder.FullName) "data"
        
        if (-not (Test-Path $dataDir)) {
            New-Item -ItemType Directory -Path $dataDir -Force
        }
        
        # Inicializar base de dados
        & $mysqldExe --initialize-insecure --datadir="$dataDir"
        Start-Sleep -Seconds 5
        
        # Tentar iniciar novamente
        Start-Service -Name $svc.Name
        Start-Sleep -Seconds 10
    }
}

# 5. Localizar mysql.exe com busca mais abrangente
Write-Host "🔍 Localizando mysql.exe..."

# Primeiro tentar no PATH
$mysqlExe = Get-Command mysql.exe -ErrorAction SilentlyContinue | Select-Object -First 1 -ExpandProperty Source

# Buscar em Program Files
if (-not $mysqlExe) {
    Write-Host "Procurando em Program Files..."
    $mysqlExe = Get-ChildItem "C:\Program Files\MariaDB*" -Recurse -Filter mysql.exe -ErrorAction SilentlyContinue |
                Select-Object -First 1 -ExpandProperty FullName
    if ($mysqlExe) {
        $mysqlDir = Split-Path $mysqlExe
        $env:PATH = "$mysqlDir;$env:PATH"
        Write-Host "Encontrado em: $mysqlExe"
    }
}

# Buscar no Chocolatey lib
if (-not $mysqlExe) {
    Write-Host "Procurando no Chocolatey..."
    $mysqlExe = Get-ChildItem "$Env:ProgramData\chocolatey\lib\mariadb*" -Recurse -Filter mysql.exe -ErrorAction SilentlyContinue |
                Select-Object -First 1 -ExpandProperty FullName
    if ($mysqlExe) {
        $mysqlDir = Split-Path $mysqlExe
        $env:PATH = "$mysqlDir;$env:PATH"
        Write-Host "Encontrado em: $mysqlExe"
    }
}

# Buscar no Chocolatey bin
if (-not $mysqlExe) {
    Write-Host "Procurando no Chocolatey bin..."
    $mysqlExe = Get-ChildItem "$Env:ProgramData\chocolatey\bin" -Filter mysql.exe -ErrorAction SilentlyContinue |
                Select-Object -First 1 -ExpandProperty FullName
    if ($mysqlExe) {
        Write-Host "Encontrado em: $mysqlExe"
    }
}

# Buscar em todas as unidades
if (-not $mysqlExe) {
    Write-Host "Procurando em todo o sistema..."
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | Select-Object -ExpandProperty DeviceID
    foreach ($drive in $drives) {
        $mysqlExe = Get-ChildItem "$drive\" -Recurse -Filter mysql.exe -ErrorAction SilentlyContinue |
                    Where-Object {$_.FullName -like "*MariaDB*"} |
                    Select-Object -First 1 -ExpandProperty FullName
        if ($mysqlExe) {
            $mysqlDir = Split-Path $mysqlExe
            $env:PATH = "$mysqlDir;$env:PATH"
            Write-Host "Encontrado em: $mysqlExe"
            break
        }
    }
}

if (-not $mysqlExe) { 
    Write-Host "❌ Listando conteúdo do Chocolatey MariaDB:"
    Get-ChildItem "$Env:ProgramData\chocolatey\lib\mariadb*" -Recurse | Select-Object FullName | Format-Table -AutoSize
    Stop-OnError "mysql.exe não encontrado em nenhum local"
}

# 6. Testar conexão com mais detalhes
Write-Host "🔑 Testando conexão com password fornecida..."
$testResult = & $mysqlExe -u root --password="$rootPassword" -e "SELECT 1 AS test;" 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Conexão bem-sucedida com a password fornecida!"
} else {
    Write-Host "⚠️ Erro na conexão: $testResult"
    Write-Host "Tentando acesso sem password..."
    
    $testNoPass = & $mysqlExe -u root -e "SELECT 1 AS test;" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "🔄 Acesso sem password detectado. Definindo nova password..."
        $setPassResult = & $mysqlExe -u root -e "SET PASSWORD FOR 'root'@'localhost' = PASSWORD('$rootPassword'); FLUSH PRIVILEGES;" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Password root configurada com sucesso!"
        } else {
            Write-Host "❌ Erro ao definir password: $setPassResult"
            Stop-OnError "Falha ao configurar password root"
        }
    } else {
        Write-Host "❌ Erro no acesso sem password: $testNoPass"
        Stop-OnError "Não foi possível aceder ao MariaDB"
    }
}

Write-Host "✅ MariaDB configurado com sucesso!"
