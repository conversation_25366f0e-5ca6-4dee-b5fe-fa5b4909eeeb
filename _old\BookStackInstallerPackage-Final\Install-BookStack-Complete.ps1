<#  Install‑BookStack‑Complete.ps1
    Instala BookStack + MariaDB + IIS (HTTP → HTTPS) num Windows Server/10
    Autor: (adaptado com correcções sugeridas)

    Requisitos:
      • PowerShell 5+ em modo Administrador
      • Acesso à Internet
      • IIS j<PERSON> instalado (Web‑Server, CGI)                # adicionar via Server Manager ou DISM
#>

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

function Stop-OnError ($Message) {
    Write-Error "❌ $Message"
    Stop-Transcript
    exit 1
}

# ----------------------------------------------------------------------
# 1. Passwords
# ----------------------------------------------------------------------
$secureRootPassword = Read-Host "Introduz a password ROOT do MariaDB" -AsSecureString
$rootPassword       = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
                        [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword))
Stop-Transcript          # evita que a password aleatória fique no log
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | ForEach-Object { [char]$_ })
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# ----------------------------------------------------------------------
# 2. Chocolatey (se faltar) + dependências
# ----------------------------------------------------------------------
if (-not (Get-Command choco.exe -ErrorAction SilentlyContinue)) {
    Write-Host "📦 A instalar Chocolatey..."
    Set-ExecutionPolicy Bypass -Scope Process -Force
    Invoke-Expression ((New-Object Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# URL Rewrite (IIS)
if (-not (Test-Path "$env:SystemRoot\System32\inetsrv\rewrite.dll")) {
    choco install urlrewrite -y               || Stop-OnError "Falha ao instalar URL Rewrite"
}

# (Opcional) VC++ Redistributable — necessário para PHP vs16
if (-not (Get-Command 'C:\Program Files (x86)\Microsoft Visual Studio\Installer\resources\app\layout\installcleanup.exe' -ErrorAction SilentlyContinue)) {
    choco install vcredist140 -y              || Stop-OnError "Falha ao instalar VC++ Redistributable"
}

# ----------------------------------------------------------------------
# 3. PHP (download dinâmico)
# ----------------------------------------------------------------------
Write-Host "🔧 A instalar PHP…"

# 3.1 – escolher o ZIP mais recente, ignorando *devel* e *debug*
$phpPage  = Invoke-WebRequest "https://windows.php.net/downloads/releases/" -UseBasicParsing
$phpLink  = ($phpPage.Links |
             Where-Object {
                 $_.href -match '-Win32-vs16-x64\.zip$' -and      # 64 bit release
                 $_.href -notmatch '(devel|debug)'                # ignora snapshots
             } |
             Sort-Object href -Descending)[0].href
if (-not $phpLink) { Stop-OnError "Não encontrei um build de release de PHP no site." }

$phpZipUrl = "https://windows.php.net$phpLink"
$phpZip    = "$env:TEMP\php.zip"

Invoke-WebRequest $phpZipUrl -OutFile $phpZip -ErrorAction Stop

# 3.2 – limpar e extrair
if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
New-Item C:\php -ItemType Directory | Out-Null
$tempDir = Join-Path $env:TEMP "php_extract"
Expand-Archive $phpZip -DestinationPath $tempDir -Force
Get-ChildItem $tempDir | ForEach-Object { Move-Item $_.FullName C:\php -Force }
Remove-Item $tempDir -Recurse

# 3.3 – localizar pasta real
$phpRoot = Get-ChildItem C:\php -Directory | Select-Object -First 1
if (-not $phpRoot) { Stop-OnError "PHP não encontrado após extracção." }

# 3.4 – garantir que existe um php.ini
$iniDev = Join-Path $phpRoot.FullName 'php.ini-development'
$phpIni = Join-Path $phpRoot.FullName 'php.ini'
if (Test-Path $iniDev) {
    Copy-Item $iniDev $phpIni -Force
} else {
    Write-Warning "php.ini-development não existe (snapshot?). A criar php.ini vazio."
    New-Item $phpIni -ItemType File | Out-Null
}

# 3.5 – activar extensões necessárias
$extensions = 'mbstring','openssl','simplexml','gd','fileinfo','curl','pdo_mysql','intl','zip'
(Get-Content $phpIni) |
  ForEach-Object {
      if    ($_ -match "^\s*;\s*extension=($($extensions -join '|'))") { $_.TrimStart(';') }
      elseif($_ -match '^;?date\.timezone') { 'date.timezone = Europe/Lisbon' }
      elseif($_ -match '^;?upload_max_filesize') { 'upload_max_filesize = 50M' }
      else { $_ }
  } | Set-Content $phpIni

# 3.6 – PATH
[Environment]::SetEnvironmentVariable('Path', "$($phpRoot.FullName);$env:Path", 'Machine')
$env:Path = "$($phpRoot.FullName);$env:Path"

# ----------------------------------------------------------------------
# 4. Composer
# ----------------------------------------------------------------------
Write-Host "🔧 A instalar Composer…"
$composerInstaller = "$env:TEMP\ComposerSetup-$PID.exe"
if (Test-Path $composerInstaller) { Remove-Item $composerInstaller -Force }
Invoke-WebRequest "https://getcomposer.org/Composer-Setup.exe" -OutFile $composerInstaller -ErrorAction Stop
Start-Process -FilePath $composerInstaller -ArgumentList '/quiet' -Wait
$composer = "$Env:ProgramFiles\ComposerSetup\bin\composer.bat"

# ----------------------------------------------------------------------
# 5. MariaDB via Chocolatey
#    (usa parâmetros oficiais do pacote mariadb para definir rootpwd)
# ----------------------------------------------------------------------
Write-Host "🛠️  A instalar MariaDB…"
choco install mariadb `
    --params "/port:3306 /rootpwd:`"$rootPassword`" /servicename:MariaDB" -y `
    || Stop-OnError "Falha a instalar MariaDB"

$mysql = Get-Command mysql.exe -ErrorAction SilentlyContinue |
         Select-Object -First 1 -ExpandProperty Source
if (-not $mysql) { Stop-OnError "mysql.exe não encontrado no PATH." }

# criar base de dados e utilizador
$creationSql = @"
DROP DATABASE IF EXISTS bookstack;
CREATE DATABASE bookstack CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@
& $mysql -u root --password="$rootPassword" -e "$creationSql" `
    || Stop-OnError "Erro a criar base de dados."

# ----------------------------------------------------------------------
# 6. Clonar BookStack + dependências PHP
# ----------------------------------------------------------------------
$bookstackFolder = "C:\inetpub\wwwroot\bookstack"
if (Test-Path $bookstackFolder) { Remove-Item $bookstackFolder -Recurse -Force }
git clone https://github.com/BookStackApp/BookStack.git $bookstackFolder `
    || Stop-OnError "Falha ao clonar BookStack"

Set-Location $bookstackFolder
& $composer install --no-dev --no-interaction || Stop-OnError "Composer install falhou"

# .env
Copy-Item '.env.example' '.env' -Force
(Get-Content '.env') |
    ForEach-Object {
        $_ -replace '^APP_URL=.*',    'APP_URL=https://localhost/bookstack' `
           -replace '^DB_DATABASE=.*','DB_DATABASE=bookstack' `
           -replace '^DB_USERNAME=.*','DB_USERNAME=bookstack_user' `
           -replace '^DB_PASSWORD=.*',"DB_PASSWORD=$bookstackUserPassword"
    } | Set-Content '.env'

php artisan key:generate    || Stop-OnError "php artisan key:generate falhou"
php artisan migrate --force || Stop-OnError "php artisan migrate falhou"

# ----------------------------------------------------------------------
# 7. Permissões (storage + cache)
# ----------------------------------------------------------------------
$aclUser = 'IIS_IUSRS'
# forma 1 – usando ${}
icacls "$bookstackFolder\storage"          /grant "${aclUser}:(OI)(CI)(M)" /T
icacls "$bookstackFolder\bootstrap\cache" /grant "${aclUser}:(OI)(CI)(M)" /T

# ----------------------------------------------------------------------
# 8. IIS – site + HTTPS
# ----------------------------------------------------------------------
Import-Module WebAdministration

# AppPool
if (-not (Get-WebAppPoolState 'BookStackAppPool' -ErrorAction SilentlyContinue)) {
    New-WebAppPool -Name 'BookStackAppPool'
    Set-ItemProperty IIS:\AppPools\BookStackAppPool -Name managedRuntimeVersion -Value ''
}

# Site
if (-not (Get-Website 'BookStack' -ErrorAction SilentlyContinue)) {
    New-Website -Name 'BookStack' -Port 80 -PhysicalPath "$bookstackFolder\public" `
                -ApplicationPool 'BookStackAppPool'
}

# Certificado auto‑assinado + binding :443
$cert = New-SelfSignedCertificate -DnsName 'localhost' -CertStoreLocation 'cert:\LocalMachine\My'
if (-not (Get-WebBinding -Name 'BookStack' -Protocol https -ErrorAction SilentlyContinue)) {
    New-WebBinding -Name 'BookStack' -Protocol https -Port 443
}
if (-not (Get-ChildItem IIS:\SslBindings | Where { $_.Port -eq 443 })) {
    New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
}

# web.config – redirect HTTP → HTTPS
Set-Content "$bookstackFolder\public\web.config" @'
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Redirect to HTTPS" stopProcessing="true">
          <match url="(.*)" />
          <conditions><add input="{HTTPS}" pattern="off" ignoreCase="true" /></conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
        </rule>
      </rules>
    </rewrite>
    <security><access sslFlags="Ssl" /></security>
  </system.webServer>
</configuration>
'@

# ----------------------------------------------------------------------
# 9. Fim
# ----------------------------------------------------------------------
Stop-Transcript
Write-Host "`n✅ Instalação concluída com sucesso!" -ForegroundColor Green
Write-Host "`n🔐 Credenciais criadas:"
Write-Host "   ➤ Utilizador BD : bookstack_user"
Write-Host "   ➤ Password BD   : $bookstackUserPassword"
Write-Host "   ➤ URL           : https://localhost/bookstack`n"
