<#  Install‑BookStack‑Complete.ps1
    Instala BookStack + MariaDB + IIS (HTTPS) num Windows Server/10
#>

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append
function Stop-OnError ($msg) { Write-Error "❌ $msg"; Stop-Transcript; exit 1 }

# ──────────────────────────────────────────────────────────────────────
# 1. PASSWORDS
# ──────────────────────────────────────────────────────────────────────
$secureRootPassword = Read-Host "Password ROOT actual do MariaDB (novo se for instalar)" -AsSecureString
$rootPassword       = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
                         [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword))

Stop-Transcript    # oculta a geração da pass aleatória
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | % { [char]$_ })
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# ──────────────────────────────────────────────────────────────────────
# 2. DEPENDÊNCIAS CHOCOLATEY
# ──────────────────────────────────────────────────────────────────────
if (-not (Get-Command choco.exe -EA SilentlyContinue)) {
    Write-Host "📦 A instalar Chocolatey…"
    Set-ExecutionPolicy Bypass -Scope Process -Force
    iex ((New-Object Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

if (-not (Test-Path "$env:SystemRoot\System32\inetsrv\rewrite.dll")) {
    choco install urlrewrite -y                || Stop-OnError "URL Rewrite falhou"
}

choco install vcredist140 -y --ignore-checksums    # ignora se já existir

# ──────────────────────────────────────────────────────────────────────
# 3. PHP (build de RELEASE, nunca *devel/debug*)
# ──────────────────────────────────────────────────────────────────────
Write-Host "🔧 A instalar PHP…"
$phpPage = Invoke-WebRequest "https://windows.php.net/downloads/releases/" -UseBasicParsing
$phpLink = ($phpPage.Links |
            ? { $_.href -match '-Win32-vs16-x64\.zip$' -and $_.href -notmatch '(devel|debug)' } |
            Sort-Object href -Descending)[0].href
if (-not $phpLink) { Stop-OnError "Nenhum build de release encontrado." }

$phpZipUrl = "https://windows.php.net$phpLink"
$phpZip    = "$env:TEMP\php.zip"

Invoke-WebRequest $phpZipUrl -OutFile $phpZip -EA Stop

if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
New-Item C:\php -ItemType Directory | Out-Null

$tempDir = Join-Path $env:TEMP php_extract
Expand-Archive $phpZip -DestinationPath $tempDir -Force
Get-ChildItem $tempDir | % { Move-Item $_.FullName C:\php -Force }
Remove-Item $tempDir -Recurse

$phpRoot = Get-ChildItem C:\php -Directory | Select-Object -First 1
if (-not $phpRoot) { Stop-OnError "Falha ao localizar pasta PHP." }

$phpIni  = Join-Path $phpRoot 'php.ini'
if (Test-Path (Join-Path $phpRoot 'php.ini-development')) {
    Copy-Item (Join-Path $phpRoot 'php.ini-development') $phpIni -Force
} else {
    Write-Warning "php.ini-development ausente; a criar php.ini vazio."
    New-Item $phpIni -ItemType File | Out-Null
}

$ext = 'mbstring','openssl','simplexml','gd','fileinfo','curl','pdo_mysql','intl','zip'
(Get-Content $phpIni) |
  % {
      if    ($_ -match "^\s*;\s*extension=($($ext -join '|'))") { $_.TrimStart(';') }
      elseif($_ -match '^;?date\.timezone')  { 'date.timezone = Europe/Lisbon' }
      elseif($_ -match '^;?upload_max_filesize') { 'upload_max_filesize = 50M' }
      else { $_ }
  } | Set-Content $phpIni

[Environment]::SetEnvironmentVariable('Path', "$($phpRoot);$env:Path", 'Machine')
$env:Path = "$($phpRoot);$env:Path"

# ──────────────────────────────────────────────────────────────────────
# 4. COMPOSER
# ──────────────────────────────────────────────────────────────────────
Write-Host "🔧 A instalar Composer…"
$composerInstaller = "$env:TEMP\ComposerSetup-$PID.exe"
if (Test-Path $composerInstaller) { Remove-Item $composerInstaller -Force }
Invoke-WebRequest https://getcomposer.org/Composer-Setup.exe -OutFile $composerInstaller -EA Stop
Start-Process $composerInstaller -ArgumentList '/quiet' -Wait
$composer = "$Env:ProgramFiles\ComposerSetup\bin\composer.bat"

# ──────────────────────────────────────────────────────────────────────
# 5. MARIADB – instalar se faltar, senão só testar login
# ──────────────────────────────────────────────────────────────────────
function Test-RootLogin {
    param($Pwd)
    $mysql = Get-Command mysql.exe -EA SilentlyContinue | Select -First 1 -Expand Source
    if ($mysql) { & $mysql -u root --password="$Pwd" -e "SELECT 1;" 2>$null }
    return $LASTEXITCODE -eq 0
}

if (-not (Get-Service MariaDB* -EA SilentlyContinue)) {
    Write-Host "🛠️  A instalar MariaDB (nova)…"
    choco install mariadb --params "/rootpwd:`"$rootPassword`" /port:3306" -y `
        || Stop-OnError "Instalação MariaDB falhou."
} elseif (-not (Test-RootLogin $rootPassword)) {
    Stop-OnError "A password fornecida não dá acesso ao root. Abortar."
} else {
    Write-Host "🔑 MariaDB já instalado e login confirmado."
}

$mysql = Get-Command mysql.exe -EA SilentlyContinue | Select -First 1 -Expand Source
if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }

$sql = @"
CREATE DATABASE IF NOT EXISTS bookstack CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@
& $mysql -u root --password="$rootPassword" -e "$sql" || Stop-OnError "Erro ao configurar BD."

# ──────────────────────────────────────────────────────────────────────
# 6. BOOKSTACK + DEPENDÊNCIAS
# ──────────────────────────────────────────────────────────────────────
$bookstackDir = "C:\inetpub\wwwroot\bookstack"
if (Test-Path $bookstackDir) { Remove-Item $bookstackDir -Recurse -Force }
git clone https://github.com/BookStackApp/BookStack.git $bookstackDir            `
    || Stop-OnError "Clone BookStack falhou."

Set-Location $bookstackDir
& $composer install --no-dev --no-interaction || Stop-OnError "Composer falhou."

Copy-Item .env.example .env -Force
(Get-Content .env) |
  % {
      $_ -replace '^APP_URL=.*',    'APP_URL=https://localhost/bookstack' `
         -replace '^DB_DATABASE=.*','DB_DATABASE=bookstack' `
         -replace '^DB_USERNAME=.*','DB_USERNAME=bookstack_user' `
         -replace '^DB_PASSWORD=.*',"DB_PASSWORD=$bookstackUserPassword"
  } | Set-Content .env

php artisan key:generate          || Stop-OnError "artisan key:generate falhou."
php artisan migrate --force       || Stop-OnError "artisan migrate falhou."

# ──────────────────────────────────────────────────────────────────────
# 7. PERMISSÕES
# ──────────────────────────────────────────────────────────────────────
$acl = 'IIS_IUSRS'
icacls "$bookstackDir\storage"           /grant "${acl}:(OI)(CI)(M)" /T
icacls "$bookstackDir\bootstrap\cache"  /grant "${acl}:(OI)(CI)(M)" /T

# ──────────────────────────────────────────────────────────────────────
# 8. IIS + HTTPS
# ──────────────────────────────────────────────────────────────────────
Import-Module WebAdministration

if (-not (Get-WebAppPoolState 'BookStackAppPool' -EA SilentlyContinue)) {
    New-WebAppPool 'BookStackAppPool'
    Set-ItemProperty IIS:\AppPools\BookStackAppPool managedRuntimeVersion ''
}

if (-not (Get-Website 'BookStack' -EA SilentlyContinue)) {
    New-Website -Name 'BookStack' -Port 80 -PhysicalPath "$bookstackDir\public" `
                -ApplicationPool 'BookStackAppPool'
}

$cert = Get-ChildItem Cert:\LocalMachine\My | ? { $_.Subject -eq 'CN=localhost' } |
        Select -First 1
if (-not $cert) { $cert = New-SelfSignedCertificate -DnsName localhost -CertStoreLocation Cert:\LocalMachine\My }

if (-not (Get-WebBinding -Name BookStack -Protocol https -EA SilentlyContinue)) {
    New-WebBinding -Name BookStack -Protocol https -Port 443
}
if (-not (Get-ChildItem IIS:\SslBindings | ? { $_.Port -eq 443 })) {
    New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
}

Set-Content "$bookstackDir\public\web.config" @'
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Redirect to HTTPS" stopProcessing="true">
          <match url="(.*)" /><conditions><add input="{HTTPS}" pattern="off" /></conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
        </rule>
      </rules>
    </rewrite>
    <security><access sslFlags="Ssl" /></security>
  </system.webServer>
</configuration>
'@

# ──────────────────────────────────────────────────────────────────────
# 9. FIM
# ──────────────────────────────────────────────────────────────────────
Stop-Transcript
Write-Host "`n✅ Instalação concluída com sucesso!"
Write-Host "`n🔐 Credenciais:"
Write-Host "   ➤ Utilizador BD : bookstack_user"
Write-Host "   ➤ Password BD   : $bookstackUserPassword"
Write-Host "   ➤ URL           : https://localhost/bookstack`n"
