<#  Install‑BookStack‑Complete.ps1
    Instala BookStack + MariaDB + IIS (HTTPS) num Windows Server / Windows 10+
#>

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append
function Stop-OnError ($msg) { Write-Error "❌ $msg"; Stop-Transcript; exit 1 }

# 1. PASSWORDS
$secureRootPassword = Read-Host "Password ROOT actual do MariaDB (novo se for instalar)" -AsSecureString
$rootPassword       = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
                         [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword))
Stop-Transcript
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | % { [char]$_ })
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# 2. CHOCOLATEY & URL Rewrite
if (-not (Get-Command choco.exe -EA SilentlyContinue)) {
    Write-Host "📦 A instalar Chocolatey…"
    Set-ExecutionPolicy Bypass -Scope Process -Force
    iex ((New-Object Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}
if (-not (Test-Path "$env:SystemRoot\System32\inetsrv\rewrite.dll")) {
    choco install urlrewrite -y                || Stop-OnError "URL Rewrite falhou"
}
choco install vcredist140 -y --ignore-checksums | Out-Null

# 3. PHP
Write-Host "🔧 A instalar PHP…"
$phpPage = Invoke-WebRequest "https://windows.php.net/downloads/releases/" -UseBasicParsing
$phpLink = ($phpPage.Links |
            Where-Object { $_.href -match '-nts-Win32-vs16-x64\.zip$' -and $_.href -notmatch '(devel|debug)' } |
            Sort-Object href -Descending |
            Select-Object -First 1).href
if (-not $phpLink) { Stop-OnError "Nenhum build de release x64 encontrado." }
$phpZipUrl = "https://windows.php.net$phpLink"
$phpZip    = "$env:TEMP\php.zip"
Invoke-WebRequest $phpZipUrl -OutFile $phpZip -EA Stop

if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
New-Item C:\php -ItemType Directory | Out-Null
Expand-Archive $phpZip -DestinationPath C:\php -Force

$phpRoot = (Get-ChildItem C:\php -Recurse -Filter php.exe | Select-Object -First 1).Directory.FullName
if (-not $phpRoot) { Stop-OnError "Falha ao localizar php.exe." }

$phpIni  = Join-Path $phpRoot 'php.ini'
$iniProd = Join-Path $phpRoot 'php.ini-production'
$iniDev  = Join-Path $phpRoot 'php.ini-development'
if     (Test-Path $iniProd) { Copy-Item $iniProd $phpIni -Force }
elseif (Test-Path $iniDev)  { Copy-Item $iniDev  $phpIni -Force }
else   { New-Item $phpIni -ItemType File | Out-Null }

$ext = 'mbstring','openssl','simplexml','gd','fileinfo','curl','pdo_mysql','intl','zip'
(Get-Content $phpIni) |
  ForEach-Object {
      if    ($_ -match "^\s*;\s*extension=($($ext -join '|'))") { $_.TrimStart(';') }
      elseif($_ -match '^;?date\.timezone')     { 'date.timezone = Europe/Lisbon' }
      elseif($_ -match '^;?upload_max_filesize') { 'upload_max_filesize = 50M' }
      else { $_ }
  } | Set-Content $phpIni

[Environment]::SetEnvironmentVariable('Path', "$phpRoot;$env:Path", 'Machine')
$env:Path = "$phpRoot;$env:Path"

# 4. COMPOSER
Write-Host "🔧 A instalar Composer…"
$composerSetup = "$env:TEMP\ComposerSetup-$PID.exe"
Invoke-WebRequest https://getcomposer.org/Composer-Setup.exe -OutFile $composerSetup -EA Stop
Start-Process $composerSetup -ArgumentList '/SILENT', '/ALLUSERS=1', '/NORESTART' -Wait

$composer = Get-Command composer -EA SilentlyContinue |
            Select-Object -First 1 -ExpandProperty Source
if (-not $composer) {
    $composer = Get-ChildItem "$Env:ProgramData\ComposerSetup","$Env:AppData\ComposerSetup" -Recurse -Filter composer.* |
                Where-Object { $_.Name -match '^composer\.(bat|cmd)$' } |
                Select-Object -First 1 -Expand FullName
}
if (-not $composer) { Stop-OnError "Composer não ficou acessível." }

# 5. MARIADB (código existente mantido igual...)
# [resto do código MariaDB igual ao original]

# 6. BOOKSTACK COM DIAGNÓSTICOS
$bookstackDir = "C:\inetpub\wwwroot\bookstack"
if (Test-Path $bookstackDir) { Remove-Item $bookstackDir -Recurse -Force }
git clone https://github.com/BookStackApp/BookStack.git $bookstackDir || Stop-OnError "Clone BookStack falhou."

Set-Location $bookstackDir
& $composer install --no-dev --no-interaction || Stop-OnError "Composer falhou."

Copy-Item .env.example .env -Force
(Get-Content .env) |
  ForEach-Object {
      $_ -replace '^APP_URL=.*',    'APP_URL=https://localhost/bookstack' `
         -replace '^DB_DATABASE=.*','DB_DATABASE=bookstack' `
         -replace '^DB_USERNAME=.*','DB_USERNAME=bookstack_user' `
         -replace '^DB_PASSWORD=.*',"DB_PASSWORD=$bookstackUserPassword"
  } | Set-Content .env

# DIAGNÓSTICO COMPLETO
Write-Host "🔍 TESTE DE CONEXÃO À BASE DE DADOS:"
Write-Host "===================================="

# Mostrar configuração
Write-Host "📄 Configuração .env:"
Get-Content .env | Where-Object { $_ -match "^(DB_|APP_URL)" } | ForEach-Object { Write-Host "   $_" }

# Teste MySQL direto
Write-Host "`n🔌 Teste MySQL direto:"
$mysqlTest = & $mysql -u bookstack_user --password="$bookstackUserPassword" -D bookstack -e "SELECT 'MySQL OK' AS test;" 2>&1
Write-Host "   $mysqlTest"

# Teste PHP
Write-Host "`n🐘 Teste PHP PDO:"
$phpTest = php -r "try { `$pdo = new PDO('mysql:host=localhost;dbname=bookstack;charset=utf8mb4', 'bookstack_user', '$bookstackUserPassword'); echo 'PHP OK'; } catch(Exception `$e) { echo 'ERRO: ' . `$e->getMessage(); exit(1); }" 2>&1
Write-Host "   $phpTest"

if ($LASTEXITCODE -ne 0) {
    Write-Host "`n🔧 Recriando utilizador..."
    $simpleSql = @"
DROP USER IF EXISTS 'bookstack_user'@'localhost';
CREATE USER 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@
    & $mysql -u root --password="$rootPassword" -e "$simpleSql"
    
    # Teste final
    $finalTest = php -r "try { `$pdo = new PDO('mysql:host=localhost;dbname=bookstack;charset=utf8mb4', 'bookstack_user', '$bookstackUserPassword'); echo 'OK'; } catch(Exception `$e) { echo 'ERRO'; exit(1); }" 2>&1
    if ($LASTEXITCODE -ne 0) { Stop-OnError "Conexão PHP impossível" }
}

Write-Host "`n✅ Conexão verificada!"

php artisan key:generate       || Stop-OnError "artisan key:generate falhou."
php artisan migrate --force    || Stop-OnError "artisan migrate falhou."

# 7. PERMISSÕES
icacls "$bookstackDir\storage"          /grant 'IIS_IUSRS:(OI)(CI)(M)' /T
icacls "$bookstackDir\bootstrap\cache" /grant 'IIS_IUSRS:(OI)(CI)(M)' /T

# 8. IIS + HTTPS
Import-Module WebAdministration
if (-not (Get-WebAppPoolState 'BookStackAppPool' -EA SilentlyContinue)) {
    New-WebAppPool 'BookStackAppPool'
    Set-ItemProperty IIS:\AppPools\BookStackAppPool managedRuntimeVersion ''
}
if (-not (Get-Website 'BookStack' -EA SilentlyContinue)) {
    New-Website -Name 'BookStack' -Port 80 -PhysicalPath "$bookstackDir\public" -ApplicationPool 'BookStackAppPool'
}
$cert = Get-ChildItem Cert:\LocalMachine\My | ? { $_.Subject -eq 'CN=localhost' } | Select -First 1
if (-not $cert) { $cert = New-SelfSignedCertificate -DnsName localhost -CertStoreLocation Cert:\LocalMachine\My }
if (-not (Get-WebBinding -Name BookStack -Protocol https -EA SilentlyContinue)) {
    New-WebBinding -Name BookStack -Protocol https -Port 443
}
if (-not (Get-ChildItem IIS:\SslBindings | ? { $_.Port -eq 443 })) {
    New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
}
Set-Content "$bookstackDir\public\web.config" @'
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Redirect to HTTPS" stopProcessing="true">
          <match url="(.*)" />
          <conditions><add input="{HTTPS}" pattern="off" /></conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
        </rule>
      </rules>
    </rewrite>
    <security><access sslFlags="Ssl" /></security>
  </system.webServer>
</configuration>
'@

# 9. DONE
Stop-Transcript
Write-Host "`n✅ Instalação concluída com sucesso!"
Write-Host "`n🔐 Credenciais:"
Write-Host "   ➤ Utilizador BD : bookstack_user"
Write-Host "   ➤ Password BD   : $bookstackUserPassword"
Write-Host "   ➤ URL           : https://localhost/bookstack`n"