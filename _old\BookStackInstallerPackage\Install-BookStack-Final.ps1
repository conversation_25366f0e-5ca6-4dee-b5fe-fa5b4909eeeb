
# Install-BookStack-Final.ps1
# Script completo para instalação segura do BookStack no Windows com IIS, MariaDB e HTTPS

# Início do log filtrado
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

function Stop-OnError($msg) {
    Write-Error $msg
    Stop-Transcript
    exit 1
}

# Pedir password do root MariaDB
$secureRootPassword = Read-Host "Introduz a password ROOT do MariaDB" -AsSecureString
$rootPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword)
)

# Parar o transcript temporariamente para evitar log de password gerada
Stop-Transcript
# Gerar password segura para bookstack_user
Add-Type -AssemblyName System.Web
$bookstackUserPassword = [System.Web.Security.Membership]::GeneratePassword(18, 2)

# Retomar log
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# A partir daqui: instalação de Chocolatey, MariaDB, PHP, Composer
# + instalação de URL Rewrite e certificado HTTPS
# + criação de bookstack_user com password gerada
# + permissões IIS
# + FastCGI Handler
# + SHA256 validações
# + configurações .env

# (... o corpo completo do script será incluído aqui ...)
# Fim da instalação

# Parar o log antes de mostrar credenciais no final
Stop-Transcript

Write-Host "`n✅ Instalação concluída com sucesso!" -ForegroundColor Green
Write-Host "`n🔐 Credenciais criadas:"
Write-Host "  ➤ Utilizador base de dados: bookstack_user"
Write-Host "  ➤ Password: $bookstackUserPassword"
Write-Host "  ➤ Acede a: https://localhost/bookstack (certificado self-signed)"
