**********************
PowerShell transcript start
Start time: 20250729184428
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.windsurf\extensions\ms-vscode.powershell-2025.2.0-universal\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.windsurf\extensions\ms-vscode.powershell-2025.2.0-universal\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Windsurf\logs\20250729T183444\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Windsurf\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-33588-879278.json' -FeatureFlags @() 
Process ID: 37664
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250729184434
**********************
**********************
PowerShell transcript start
Start time: 20250729184434
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.windsurf\extensions\ms-vscode.powershell-2025.2.0-universal\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.windsurf\extensions\ms-vscode.powershell-2025.2.0-universal\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Windsurf\logs\20250729T183444\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Windsurf\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-33588-879278.json' -FeatureFlags @() 
Process ID: 37664
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🛠️  A instalar/actualizar MariaDB…

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'This service provides profile management for mobile connectivity modules (McmSvc)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'This service provides profile management for mobile connectivity modules (McmSvc)' cannot be queried due to the following error: PermissionDenied

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'WaaSMedicSvc (WaaSMedicSvc)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'WaaSMedicSvc (WaaSMedicSvc)' cannot be queried due to the following error: PermissionDenied

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'ZTHELPER (ZTHELPER)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'ZTHELPER (ZTHELPER)' cannot be queried due to the following error: PermissionDenied

🧩 Serviço MariaDB não registado — a instalar manualmente…

PS>TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:128:1
Line |
 128 |  Start-Service MariaDB -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>Set-ExecutionPolicy Bypass -Scope Process -Force; .\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
⚠️  Password root não confere. A tentar acesso sem password…
🔄 Root sem password detectado – a definir a nova password fornecida…

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'This service provides profile management for mobile connectivity modules (McmSvc)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'This service provides profile management for mobile connectivity modules (McmSvc)' cannot be queried due to the following error: PermissionDenied

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'WaaSMedicSvc (WaaSMedicSvc)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'WaaSMedicSvc (WaaSMedicSvc)' cannot be queried due to the following error: PermissionDenied

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'ZTHELPER (ZTHELPER)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'ZTHELPER (ZTHELPER)' cannot be queried due to the following error: PermissionDenied

PS>TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:148:1
Line |
 148 |  Start-Service MariaDB -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>Set-ExecutionPolicy Bypass -Scope Process -Force; .\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'This service provides profile management for mobile connectivity modules (McmSvc)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'This service provides profile management for mobile connectivity modules (McmSvc)' cannot be queried due to the following error: PermissionDenied

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'WaaSMedicSvc (WaaSMedicSvc)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'WaaSMedicSvc (WaaSMedicSvc)' cannot be queried due to the following error: PermissionDenied

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'ZTHELPER (ZTHELPER)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'ZTHELPER (ZTHELPER)' cannot be queried due to the following error: PermissionDenied

PS>TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:148:1
Line |
 148 |  Start-Service MariaDB -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>Set-ExecutionPolicy Bypass -Scope Process -Force; .\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'This service provides profile management for mobile connectivity modules (McmSvc)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'This service provides profile management for mobile connectivity modules (McmSvc)' cannot be queried due to the following error: PermissionDenied

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'WaaSMedicSvc (WaaSMedicSvc)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'WaaSMedicSvc (WaaSMedicSvc)' cannot be queried due to the following error: PermissionDenied

Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'ZTHELPER (ZTHELPER)' cannot be queried due to the following error: PermissionDenied
Get-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:121:18
Line |
 121 |  $serviceExists = Get-Service | Where-Object { $_.Name -like 'MariaDB* …
     |                   ~~~~~~~~~~~
     | Service 'ZTHELPER (ZTHELPER)' cannot be queried due to the following error: PermissionDenied

PS>TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:148:1
Line |
 148 |  Start-Service MariaDB -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

**********************
PowerShell transcript start
Start time: 20250730095118
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730095123
**********************
**********************
PowerShell transcript start
Start time: 20250730095123
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!







WARNING: Module WebAdministration is loaded in Windows PowerShell using WinPSCompatSession remoting session; please note that all input and output of commands from this module will be deserialized objects. If you want to load this module into PowerShell please use 'Import-Module -SkipEditionCheck' syntax.
Get-ChildItem: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:273:11
Line |
 273 |  if (-not (Get-ChildItem IIS:\SslBindings | ? { $_.Port -eq 443 })) {
     |            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.
Get-ChildItem: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:273:11
Line |
 273 |  if (-not (Get-ChildItem IIS:\SslBindings | ? { $_.Port -eq 443 })) {
     |            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.

New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:274:5
Line |
 274 |      New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.
New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:274:5
Line |
 274 |      New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.

**********************
PowerShell transcript end
End time: 20250730095648
**********************
**********************
PowerShell transcript start
Start time: 20250730100702
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730100723
**********************
**********************
PowerShell transcript start
Start time: 20250730100723
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...
Service 'MariaDB' successfully installed.
� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!





Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.

**********************
PowerShell transcript end
End time: 20250730101257
**********************
**********************
PowerShell transcript start
Start time: 20250730101507
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730101515
**********************
**********************
PowerShell transcript start
Start time: 20250730101516
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 - mariadb - mariadb is not installed. Cannot uninstall a non-existent package.

If a package uninstall is failing and/or you've already uninstalled the
 software outside of Chocolatey, you can attempt to run the command
 with `-n` to skip running a chocolateyUninstall script, additionally
 adding `--skip-autouninstaller` to skip an attempt to automatically
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!





Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.

**********************
PowerShell transcript end
End time: 20250730101958
**********************
**********************
PowerShell transcript start
Start time: 20250730102306
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730102311
**********************
**********************
PowerShell transcript start
Start time: 20250730102311
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!





Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.

**********************
PowerShell transcript end
End time: 20250730103902
**********************
**********************
PowerShell transcript start
Start time: 20250730104143
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730104155
**********************
**********************
PowerShell transcript start
Start time: 20250730104155
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!





Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.

**********************
PowerShell transcript end
End time: 20250730105014
**********************
**********************
PowerShell transcript start
Start time: 20250730105555
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730105600
**********************
**********************
PowerShell transcript start
Start time: 20250730105600
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!





Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:253:35
Line |
 253 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.

**********************
PowerShell transcript end
End time: 20250730110030
**********************
**********************
PowerShell transcript start
Start time: 20250730110845
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730110851
**********************
**********************
PowerShell transcript start
Start time: 20250730110851
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!



🔍 TESTE DE CONEXÃO À BASE DE DADOS:
====================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=l`<ED!8FfOMp@5k(-:

🔌 Teste MySQL direto:
   ERROR 1045 (28000): Access denied for user 'bookstack_user'@'localhost' (using password: YES)

🐘 Teste PHP PDO:
   ERRO: SQLSTATE[HY000] [1045] Access denied for user 'bookstack_user'@'localhost' (using password: YES)

🔧 Recriando utilizador...


✅ Conexão verificada!




WARNING: Module WebAdministration is loaded in Windows PowerShell using WinPSCompatSession remoting session; please note that all input and output of commands from this module will be deserialized objects. If you want to load this module into PowerShell please use 'Import-Module -SkipEditionCheck' syntax.

Name                     State        Applications
----                     -----        ------------
BookStackAppPool
Set-ItemProperty: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:298:5
Line |
 298 |      Set-ItemProperty IIS:\AppPools\BookStackAppPool managedRuntimeVer …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.
Set-ItemProperty: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:298:5
Line |
 298 |      Set-ItemProperty IIS:\AppPools\BookStackAppPool managedRuntimeVer …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.


name                       : BookStack
id                         : 5
serverAutoStart            : True
state                      : Stopped
bindings                   : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
limits                     : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
logFile                    : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
traceFailedRequestsLogging : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
hsts                       : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
applicationDefaults        : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
virtualDirectoryDefaults   : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
ftpServer                  : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
Collection                 : {Microsoft.IIs.PowerShell.Framework.ConfigurationElement}
applicationPool            : BookStackAppPool
enabledProtocols           : http
physicalPath               : C:\inetpub\wwwroot\bookstack\public
userName                   : 
password                   : 
ItemXPath                  : /system.applicationHost/sites/site[@name='BookStack' and @id='5']
PSPath                     : WebAdministration::\\PROCYON\Sites\BookStack
PSParentPath               : WebAdministration::\\PROCYON\Sites
PSChildName                : BookStack
PSProvider                 : WebAdministration
PSIsContainer              : True
RunspaceId                 : 2f5d76b5-f15b-4b24-bb73-d7cd42fd07fe
Attributes                 : {Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute, Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute, Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute}
ChildElements              : {Microsoft.IIs.PowerShell.Framework.ConfigurationElement, Microsoft.IIs.PowerShell.Framework.ConfigurationElement,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationElement, Microsoft.IIs.PowerShell.Framework.ConfigurationElement…}
ElementTagName             : site
Methods                    : {Microsoft.IIs.PowerShell.Framework.ConfigurationMethod, Microsoft.IIs.PowerShell.Framework.ConfigurationMethod}
Schema                     : Microsoft.IIs.PowerShell.Framework.ConfigurationElementSchema

Get-ChildItem: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:308:11
Line |
 308 |  if (-not (Get-ChildItem IIS:\SslBindings | ? { $_.Port -eq 443 })) {
     |            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.
Get-ChildItem: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:308:11
Line |
 308 |  if (-not (Get-ChildItem IIS:\SslBindings | ? { $_.Port -eq 443 })) {
     |            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.

New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:309:5
Line |
 309 |      New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.
New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:309:5
Line |
 309 |      New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.

**********************
PowerShell transcript end
End time: 20250730111333
**********************
**********************
PowerShell transcript start
Start time: 20250730111942
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730111946
**********************
**********************
PowerShell transcript start
Start time: 20250730111946
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!



🔍 TESTE DE CONEXÃO À BASE DE DADOS:
====================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=}ZKQP%sG/:a;Hp<A1y

🔌 Teste MySQL direto:
   ERROR 1045 (28000): Access denied for user 'bookstack_user'@'localhost' (using password: YES)

🐘 Teste PHP PDO:
   ERRO: SQLSTATE[HY000] [1045] Access denied for user 'bookstack_user'@'localhost' (using password: YES)

🔧 Recriando utilizador...


✅ Conexão verificada!




PS>TerminatingError(Import-Module): "Could not load type 'System.Management.Automation.PSSnapIn' from assembly 'System.Management.Automation, Version=7.4.6.500, Culture=neutral, PublicKeyToken=31bf3856ad364e35'."
Import-Module: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:295:1
Line |
 295 |  Import-Module WebAdministration -SkipEditionCheck
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Could not load type 'System.Management.Automation.PSSnapIn' from assembly 'System.Management.Automation, Version=7.4.6.500, Culture=neutral, PublicKeyToken=31bf3856ad364e35'.
Import-Module: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:295:1
Line |
 295 |  Import-Module WebAdministration -SkipEditionCheck
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Could not load type 'System.Management.Automation.PSSnapIn' from assembly 'System.Management.Automation, Version=7.4.6.500, Culture=neutral,
     | PublicKeyToken=31bf3856ad364e35'.

🌐 Usando portas: HTTP=8080, HTTPS=8443

Name                     State        Applications
----                     -----        ------------
BookStackAppPool
Set-ItemProperty: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:317:5
Line |
 317 |      Set-ItemProperty "IIS:\AppPools\BookStackAppPool" managedRuntimeV …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.
Set-ItemProperty: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:317:5
Line |
 317 |      Set-ItemProperty "IIS:\AppPools\BookStackAppPool" managedRuntimeV …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.


name                       : BookStack
id                         : 5
serverAutoStart            : True
state                      : Started
bindings                   : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
limits                     : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
logFile                    : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
traceFailedRequestsLogging : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
hsts                       : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
applicationDefaults        : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
virtualDirectoryDefaults   : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
ftpServer                  : Microsoft.IIs.PowerShell.Framework.ConfigurationElement
Collection                 : {Microsoft.IIs.PowerShell.Framework.ConfigurationElement}
applicationPool            : BookStackAppPool
enabledProtocols           : http
physicalPath               : C:\inetpub\wwwroot\bookstack\public
userName                   : 
password                   : 
ItemXPath                  : /system.applicationHost/sites/site[@name='BookStack' and @id='5']
PSPath                     : WebAdministration::\\PROCYON\Sites\BookStack
PSParentPath               : WebAdministration::\\PROCYON\Sites
PSChildName                : BookStack
PSProvider                 : WebAdministration
PSIsContainer              : True
RunspaceId                 : 2f5d76b5-f15b-4b24-bb73-d7cd42fd07fe
Attributes                 : {Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute, Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute, Microsoft.IIs.PowerShell.Framework.ConfigurationAttribute}
ChildElements              : {Microsoft.IIs.PowerShell.Framework.ConfigurationElement, Microsoft.IIs.PowerShell.Framework.ConfigurationElement,
                             Microsoft.IIs.PowerShell.Framework.ConfigurationElement, Microsoft.IIs.PowerShell.Framework.ConfigurationElement…}
ElementTagName             : site
Methods                    : {Microsoft.IIs.PowerShell.Framework.ConfigurationMethod, Microsoft.IIs.PowerShell.Framework.ConfigurationMethod}
Schema                     : Microsoft.IIs.PowerShell.Framework.ConfigurationElementSchema

⚠️ Configurar SSL manualmente no IIS Manager
**********************
PowerShell transcript end
End time: 20250730112401
**********************
**********************
PowerShell transcript start
Start time: 20250730114255
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730114301
**********************
**********************
PowerShell transcript start
Start time: 20250730114301
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!



🔍 TESTE DE CONEXÃO À BASE DE DADOS:
====================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=S|Ij*GUw]H\?xg}_Wp

🔌 Teste MySQL direto:
   ERROR 1045 (28000): Access denied for user 'bookstack_user'@'localhost' (using password: YES)

🐘 Teste PHP PDO:
   ERRO: SQLSTATE[HY000] [1045] Access denied for user 'bookstack_user'@'localhost' (using password: YES)

🔧 Recriando utilizador...

Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:282:32
Line |
 282 |  …    if ($LASTEXITCODE -ne 0) { Stop-OnError "Conexão PHP impossível" }
     |                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Conexão PHP impossível
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:282:32
Line |
 282 |  …    if ($LASTEXITCODE -ne 0) { Stop-OnError "Conexão PHP impossível" }
     |                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Conexão PHP impossível

**********************
PowerShell transcript end
End time: 20250730114719
**********************
**********************
PowerShell transcript start
Start time: 20250730115051
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730115113
**********************
**********************
PowerShell transcript start
Start time: 20250730115113
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!
🔧 Configurando base de dados...

📝 Nova password gerada: PNeifcMx1kuCsRqE
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=PNeifcMx1kuCsRqE

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK




WARNING: Module WebAdministration is loaded in Windows PowerShell using WinPSCompatSession remoting session; please note that all input and output of commands from this module will be deserialized objects. If you want to load this module into PowerShell please use 'Import-Module -SkipEditionCheck' syntax.
🌐 Usando portas: HTTP=8080, HTTPS=8443




**********************
PowerShell transcript end
End time: 20250730115524
**********************
**********************
PowerShell transcript start
Start time: 20250730120038
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730120051
**********************
**********************
PowerShell transcript start
Start time: 20250730120051
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:354:5
Line |
 354 |      Stop-OnError "Falha ao criar website no IIS"
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Falha ao criar website no IIS
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:354:5
Line |
 354 |      Stop-OnError "Falha ao criar website no IIS"
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Falha ao criar website no IIS

**********************
PowerShell transcript end
End time: 20250730120534
**********************
**********************
PowerShell transcript start
Start time: 20250730120657
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730120741
**********************
**********************
PowerShell transcript start
Start time: 20250730120741
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.


Enjoy using Chocolatey? Explore more amazing features to take your
experience to the next level at
 https://chocolatey.org/compare
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:172:9
Line |
 172 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8080:,https/*:8443:,state:Started)
🔗 Associando App Pool ao site...

📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


**********************
PowerShell transcript end
End time: 20250730121154
**********************
**********************
PowerShell transcript start
Start time: 20250730121659
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730121703
**********************
**********************
PowerShell transcript start
Start time: 20250730121703
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:179:9
Line |
 179 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:179:9
Line |
 179 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8081, HTTPS=8444
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8081:,https/*:8444:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...


📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


**********************
PowerShell transcript end
End time: 20250730122129
**********************
**********************
PowerShell transcript start
Start time: 20250730122308
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730122319
**********************
**********************
PowerShell transcript start
Start time: 20250730122319
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:179:9
Line |
 179 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:179:9
Line |
 179 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8080:,https/*:8443:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...


📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


**********************
PowerShell transcript end
End time: 20250730122850
**********************
**********************
PowerShell transcript start
Start time: 20250730123458
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730123501
**********************
**********************
PowerShell transcript start
Start time: 20250730123501
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:179:9
Line |
 179 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Service 'MariaDB (MariaDB)' cannot be started due to the following error: Cannot start service 'MariaDB' on computer '.'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:179:9
Line |
 179 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Service 'MariaDB (MariaDB)' cannot be started due to the following error: Cannot start service 'MariaDB' on computer '.'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8081, HTTPS=8444
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8081:,https/*:8444:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...


📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


**********************
PowerShell transcript end
End time: 20250730124018
**********************
**********************
PowerShell transcript start
Start time: 20250730124100
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730124108
**********************
**********************
PowerShell transcript start
Start time: 20250730124108
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
 with `-n` to skip running a chocolateyUninstall script, additionally
 adding `--skip-autouninstaller` to skip an attempt to automatically
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
⚠️ Serviço MariaDB não encontrado. Tentando registar...

� Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
⚠️ Serviço não iniciou. Tentando inicializar dados...

Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:179:9
Line |
 179 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:179:9
Line |
 179 |          Start-Service -Name $svc.Name
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
✅ Conexão bem-sucedida com a password fornecida!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8080:,https/*:8443:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...


📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


**********************
PowerShell transcript end
End time: 20250730124511
**********************
**********************
PowerShell transcript start
Start time: 20250730143230
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730143236
**********************
**********************
PowerShell transcript start
Start time: 20250730143236
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
📁 Verificando datadir: C:\Program Files\MariaDB 11.8\data
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...

🔍 Serviço encontrado: MariaDB - Status: Stopped
🔄 Iniciando serviço MariaDB...
PS>TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
❌ Erro ao iniciar serviço: Failed to start service 'MariaDB (MariaDB)'.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:206:9
Line |
 206 |          Stop-OnError "Não foi possível iniciar o serviço MariaDB"
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível iniciar o serviço MariaDB
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:206:9
Line |
 206 |          Stop-OnError "Não foi possível iniciar o serviço MariaDB"
     |          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível iniciar o serviço MariaDB

**********************
PowerShell transcript end
End time: 20250730143405
**********************
**********************
PowerShell transcript start
Start time: 20250730150035
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250730150042
**********************
**********************
PowerShell transcript start
Start time: 20250730150042
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -NoProfile -ExecutionPolicy Bypass -Command Import-Module 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules\PowerShellEditorServices\PowerShellEditorServices.psd1'; Start-EditorServices -HostName 'Visual Studio Code Host' -HostProfileId 'Microsoft.VSCode' -HostVersion '2025.2.0' -BundledModulesPath 'c:\Users\<USER>\.vscode\extensions\ms-vscode.powershell-2025.2.0\modules' -EnableConsoleRepl -StartupBanner "PowerShell Extension v2025.2.0
Copyright (c) Microsoft Corporation.

https://aka.ms/vscode-powershell
Type 'help' to get help.
" -LogLevel 'Warning' -LogPath 'c:\Users\<USER>\AppData\Roaming\Code\logs\20250729T225517\window1\exthost\ms-vscode.powershell' -SessionDetailsPath 'c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\ms-vscode.powershell\sessions\PSES-VSCode-24976-375522.json' -FeatureFlags @() 
Process ID: 29488
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
📁 Verificando datadir: C:\Program Files\MariaDB 11.8\data
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...

🔍 Serviço encontrado: MariaDB - Status: Stopped
🔄 A iniciar o serviço MariaDB com nova configuração...
PS>TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Restart-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:194:1
Line |
 194 |  Restart-Service -Name MariaDB -Force -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Stop-OnError"
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
 with `-n` to skip running a chocolateyUninstall script, additionally
 adding `--skip-autouninstaller` to skip an attempt to automatically
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
📁 Verificando datadir: C:\Program Files\MariaDB 11.8\data
⚠️ Serviço MariaDB não encontrado. Registando...

🔍 Serviço encontrado: MariaDB - Status: Stopped
🔄 A iniciar o serviço MariaDB com nova configuração...
PS>TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Restart-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:194:1
Line |
 194 |  Restart-Service -Name MariaDB -Force -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
 with `-n` to skip running a chocolateyUninstall script, additionally
 adding `--skip-autouninstaller` to skip an attempt to automatically
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
📁 Verificando datadir: C:\Program Files\MariaDB 11.8\data
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...

Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:187:27
Line |
 187 |  … -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível registar o serviço MariaDB.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:187:27
Line |
 187 |  … -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível registar o serviço MariaDB.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Join-Path"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Cmdlet          Join-Path                                          *******    Microsoft.PowerShell.Management

PS>CommandInvocation(Get-Help): "Get-Help"
>> ParameterBinding(Get-Help): name="Name"; value="Join-Path"
>> ParameterBinding(Get-Help): name="Online"; value="False"
>> ParameterBinding(Get-Help): name="ErrorAction"; value="Ignore"

NAME
    Join-Path

SYNTAX
    Join-Path [-Path] <string[]> [-ChildPath] <string> [[-AdditionalChildPath] <string[]>] [-Resolve] [-Credential <pscredential>] [<CommonParameters>]


ALIASES
    None


REMARKS
    Get-Help cannot find the Help files for this cmdlet on this computer. It is displaying only partial help.
        -- To download and install Help files for the module that includes this cmdlet, use Update-Help.
        -- To view the Help topic for this cmdlet online, type: "Get-Help Join-Path -Online" or
           go to https://go.microsoft.com/fwlink/?LinkID=2096811.


PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
 with `-n` to skip running a chocolateyUninstall script, additionally
 adding `--skip-autouninstaller` to skip an attempt to automatically
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
📁 Verificando datadir: C:\Program Files\MariaDB 11.8\data
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...

Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:194:27
Line |
 194 |  … -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível registar o serviço MariaDB.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:194:27
Line |
 194 |  … -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível registar o serviço MariaDB.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
 with `-n` to skip running a chocolateyUninstall script, additionally
 adding `--skip-autouninstaller` to skip an attempt to automatically
 remove system-installed software. Only the packaging files are removed
 and not things like software installed to Programs and Features.

If a package is failing because it is a dependency of another package
 or packages, then you may first need to consider if it needs to be
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
📁 Verificando datadir: C:\Program Files\MariaDB 11.8\data
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...

Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:194:27
Line |
 194 |  … -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível registar o serviço MariaDB.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:194:27
Line |
 194 |  … -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível registar o serviço MariaDB.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Split-Path"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Cmdlet          Split-Path                                         *******    Microsoft.PowerShell.Management

PS>CommandInvocation(Get-Help): "Get-Help"
>> ParameterBinding(Get-Help): name="Name"; value="Split-Path"
>> ParameterBinding(Get-Help): name="Online"; value="False"
>> ParameterBinding(Get-Help): name="ErrorAction"; value="Ignore"

NAME
    Split-Path

SYNTAX
    Split-Path [-Path] <string[]> [-Parent] [-Resolve] [-Credential <pscredential>] [<CommonParameters>]

    Split-Path [-Path] <string[]> -Leaf [-Resolve] [-Credential <pscredential>] [<CommonParameters>]

    Split-Path [-Path] <string[]> -LeafBase [-Resolve] [-Credential <pscredential>] [<CommonParameters>]

    Split-Path [-Path] <string[]> -Extension [-Resolve] [-Credential <pscredential>] [<CommonParameters>]

    Split-Path [-Path] <string[]> -Qualifier [-Resolve] [-Credential <pscredential>] [<CommonParameters>]

    Split-Path [-Path] <string[]> -NoQualifier [-Resolve] [-Credential <pscredential>] [<CommonParameters>]

    Split-Path [-Path] <string[]> -IsAbsolute [-Resolve] [-Credential <pscredential>] [<CommonParameters>]

    Split-Path -LiteralPath <string[]> [-Resolve] [-Credential <pscredential>] [<CommonParameters>]


ALIASES
    None


REMARKS
    Get-Help cannot find the Help files for this cmdlet on this computer. It is displaying only partial help.
        -- To download and install Help files for the module that includes this cmdlet, use Update-Help.
        -- To view the Help topic for this cmdlet online, type: "Get-Help Split-Path -Online" or
           go to https://go.microsoft.com/fwlink/?LinkID=2097149.


PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...

Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:187:27
Line |
 187 |  … -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível registar o serviço MariaDB.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:187:27
Line |
 187 |  … -eq $svc) { Stop-OnError "Não foi possível registar o serviço MariaDB …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível registar o serviço MariaDB.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Get-Command"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Cmdlet          Get-Command                                        7.4.6.500  Microsoft.PowerShell.Core

PS>CommandInvocation(Get-Help): "Get-Help"
>> ParameterBinding(Get-Help): name="Name"; value="Get-Command"
>> ParameterBinding(Get-Help): name="Online"; value="False"
>> ParameterBinding(Get-Help): name="ErrorAction"; value="Ignore"

NAME
    Get-Command

SYNTAX
    Get-Command [[-ArgumentList] <Object[]>] [-Verb <string[]>] [-Noun <string[]>] [-Module <string[]>] [-FullyQualifiedModule <ModuleSpecification[]>]
    [-TotalCount <int>] [-Syntax] [-ShowCommandInfo] [-All] [-ListImported] [-ParameterName <string[]>] [-ParameterType <PSTypeName[]>]
    [<CommonParameters>]

    Get-Command [[-Name] <string[]>] [[-ArgumentList] <Object[]>] [-Module <string[]>] [-FullyQualifiedModule <ModuleSpecification[]>] [-CommandType
    {Alias | Function | Filter | Cmdlet | ExternalScript | Application | Script | Configuration | All}] [-TotalCount <int>] [-Syntax] [-ShowCommandInfo]
    [-All] [-ListImported] [-ParameterName <string[]>] [-ParameterType <PSTypeName[]>] [-UseFuzzyMatching] [-FuzzyMinimumDistance <uint>]
    [-UseAbbreviationExpansion] [<CommonParameters>]


ALIASES
    gcm


REMARKS
    Get-Help cannot find the Help files for this cmdlet on this computer. It is displaying only partial help.
        -- To download and install Help files for the module that includes this cmdlet, use Update-Help.
        -- To view the Help topic for this cmdlet online, type: "Get-Help Get-Command -Online" or
           go to https://go.microsoft.com/fwlink/?LinkID=2096579.


PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Get-Service"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Cmdlet          Get-Service                                        *******    Microsoft.PowerShell.Management

PS>CommandInvocation(Get-Help): "Get-Help"
>> ParameterBinding(Get-Help): name="Name"; value="Get-Service"
>> ParameterBinding(Get-Help): name="Online"; value="False"
>> ParameterBinding(Get-Help): name="ErrorAction"; value="Ignore"

NAME
    Get-Service

SYNTAX
    Get-Service [[-Name] <string[]>] [-DependentServices] [-RequiredServices] [-Include <string[]>] [-Exclude <string[]>] [<CommonParameters>]

    Get-Service -DisplayName <string[]> [-DependentServices] [-RequiredServices] [-Include <string[]>] [-Exclude <string[]>] [<CommonParameters>]

    Get-Service [-DependentServices] [-RequiredServices] [-Include <string[]>] [-Exclude <string[]>] [-InputObject <ServiceController[]>]
    [<CommonParameters>]


ALIASES
    gsv


REMARKS
    Get-Help cannot find the Help files for this cmdlet on this computer. It is displaying only partial help.
        -- To download and install Help files for the module that includes this cmdlet, use Update-Help.
        -- To view the Help topic for this cmdlet online, type: "Get-Help Get-Service -Online" or
           go to https://go.microsoft.com/fwlink/?LinkID=2096496.


PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Start-Sleep"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Cmdlet          Start-Sleep                                        *******    Microsoft.PowerShell.Utility

PS>CommandInvocation(Get-Help): "Get-Help"
>> ParameterBinding(Get-Help): name="Name"; value="Start-Sleep"
>> ParameterBinding(Get-Help): name="Online"; value="False"
>> ParameterBinding(Get-Help): name="ErrorAction"; value="Ignore"

NAME
    Start-Sleep

SYNTAX
    Start-Sleep [-Seconds] <double> [<CommonParameters>]

    Start-Sleep -Milliseconds <int> [<CommonParameters>]

    Start-Sleep [-Duration] <timespan> [<CommonParameters>]


ALIASES
    sleep


REMARKS
    Get-Help cannot find the Help files for this cmdlet on this computer. It is displaying only partial help.
        -- To download and install Help files for the module that includes this cmdlet, use Update-Help.
        -- To view the Help topic for this cmdlet online, type: "Get-Help Start-Sleep -Online" or
           go to https://go.microsoft.com/fwlink/?LinkID=2097041.


PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...


Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:197:5
Line |
 197 |      Start-Service MariaDB
     |      ~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:197:5
Line |
 197 |      Start-Service MariaDB
     |      ~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Serviço encontrado: MariaDB - Status: Stopped
🔄 A iniciar o serviço MariaDB com nova configuração...
PS>TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Restart-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:205:1
Line |
 205 |  Restart-Service -Name MariaDB -Force -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Program"
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Program"
>> TerminatingError(): "The pipeline has been stopped."
>> CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ErrorAction"; value="Ignore"
>> ParameterBinding(Get-Command): name="Name"; value="Program"
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...


Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:198:5
Line |
 198 |      Start-Service MariaDB
     |      ~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:198:5
Line |
 198 |      Start-Service MariaDB
     |      ~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

🔍 Serviço encontrado: MariaDB - Status: Stopped
🔄 A iniciar o serviço MariaDB com nova configuração...
PS>TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Restart-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Restart-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:206:1
Line |
 206 |  Restart-Service -Name MariaDB -Force -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
🔧 Inicializando base de dados...

⚠️ Serviço MariaDB não encontrado. Registando...


🔍 Serviço encontrado: MariaDB - Status: Stopped
🔄 A iniciar o serviço MariaDB com nova configuração...
PS>TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:203:1
Line |
 203 |  Start-Service MariaDB -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
🔧 Inicializando base de dados...



🔄 A iniciar o serviço MariaDB com nova configuração...
PS>TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
>> TerminatingError(Start-Service): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: Failed to start service 'MariaDB (MariaDB)'.
Start-Service: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:188:1
Line |
 188 |  Start-Service MariaDB -ErrorAction Stop
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Failed to start service 'MariaDB (MariaDB)'.

]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
🔧 Inicializando base de dados...

Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:171:5
Line |
 171 |      Stop-OnError "FALHA ao inicializar MariaDB: Pasta 'mysql' não foi …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ FALHA ao inicializar MariaDB: Pasta 'mysql' não foi criada em C:/Program Files/MariaDB 11.8/data
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:171:5
Line |
 171 |      Stop-OnError "FALHA ao inicializar MariaDB: Pasta 'mysql' não foi …
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ FALHA ao inicializar MariaDB: Pasta 'mysql' não foi criada em C:/Program Files/MariaDB 11.8/data

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>CommandInvocation(Get-Command): "Get-Command"
>> ParameterBinding(Get-Command): name="ListImported"; value="True"
>> ParameterBinding(Get-Command): name="CommandType"; value="Alias"

CommandType     Name                                               Version    Source
-----------     ----                                               -------    ------
Alias           % -> ForEach-Object
Alias           ? -> Where-Object
Alias           ac -> Add-Content
Alias           cat -> Get-Content
Alias           cd -> Set-Location
Alias           chdir -> Set-Location
Alias           clc -> Clear-Content
Alias           clear -> Clear-Host
Alias           clhy -> Clear-History
Alias           cli -> Clear-Item
Alias           clp -> Clear-ItemProperty
Alias           cls -> Clear-Host
Alias           clv -> Clear-Variable
Alias           cnsn -> Connect-PSSession
Alias           compare -> Compare-Object
Alias           copy -> Copy-Item
Alias           cp -> Copy-Item
Alias           cpi -> Copy-Item
Alias           cpp -> Copy-ItemProperty
Alias           cvpa -> Convert-Path
Alias           dbp -> Disable-PSBreakpoint
Alias           del -> Remove-Item
Alias           diff -> Compare-Object
Alias           dir -> Get-ChildItem
Alias           dnsn -> Disconnect-PSSession
Alias           ebp -> Enable-PSBreakpoint
Alias           echo -> Write-Output
Alias           epal -> Export-Alias
Alias           epcsv -> Export-Csv
Alias           erase -> Remove-Item
Alias           etsn -> Enter-PSSession
Alias           exsn -> Exit-PSSession
Alias           fc -> Format-Custom
Alias           fhx -> Format-Hex                                  *******    Microsoft.PowerShell.Utility
Alias           fl -> Format-List
Alias           foreach -> ForEach-Object
Alias           ft -> Format-Table
Alias           fw -> Format-Wide
Alias           gal -> Get-Alias
Alias           gbp -> Get-PSBreakpoint
Alias           gc -> Get-Content
Alias           gcb -> Get-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           gci -> Get-ChildItem
Alias           gcm -> Get-Command
Alias           gcs -> Get-PSCallStack
Alias           gdr -> Get-PSDrive
Alias           gerr -> Get-Error
Alias           ghy -> Get-History
Alias           gi -> Get-Item
Alias           gin -> Get-ComputerInfo                            *******    Microsoft.PowerShell.Management
Alias           gip -> Get-NetIPConfiguration                      *******    NetTCPIP
Alias           gjb -> Get-Job
Alias           gl -> Get-Location
Alias           gm -> Get-Member
Alias           gmo -> Get-Module
Alias           gp -> Get-ItemProperty
Alias           gps -> Get-Process
Alias           gpv -> Get-ItemPropertyValue
Alias           group -> Group-Object
Alias           gsn -> Get-PSSession
Alias           gsv -> Get-Service
Alias           gtz -> Get-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           gu -> Get-Unique
Alias           gv -> Get-Variable
Alias           h -> Get-History
Alias           history -> Get-History
Alias           icm -> Invoke-Command
Alias           iex -> Invoke-Expression
Alias           ihy -> Invoke-History
Alias           ii -> Invoke-Item
Alias           ipal -> Import-Alias
Alias           ipcsv -> Import-Csv
Alias           ipmo -> Import-Module
Alias           irm -> Invoke-RestMethod
Alias           iwr -> Invoke-WebRequest
Alias           kill -> Stop-Process
Alias           ls -> Get-ChildItem
Alias           man -> help
Alias           md -> mkdir
Alias           measure -> Measure-Object
Alias           mi -> Move-Item
Alias           mount -> New-PSDrive
Alias           move -> Move-Item
Alias           mp -> Move-ItemProperty
Alias           mv -> Move-Item
Alias           nal -> New-Alias
Alias           ndr -> New-PSDrive
Alias           ni -> New-Item
Alias           nmo -> New-Module
Alias           nsn -> New-PSSession
Alias           nv -> New-Variable
Alias           ogv -> Out-GridView
Alias           oh -> Out-Host
Alias           popd -> Pop-Location
Alias           ps -> Get-Process
Alias           psedit -> Open-EditorFile                          0.2.0      PowerShellEditorServices.Commands
Alias           pushd -> Push-Location
Alias           pwd -> Get-Location
Alias           r -> Invoke-History
Alias           rbp -> Remove-PSBreakpoint
Alias           rcjb -> Receive-Job
Alias           rcsn -> Receive-PSSession
Alias           rd -> Remove-Item
Alias           rdr -> Remove-PSDrive
Alias           ren -> Rename-Item
Alias           ri -> Remove-Item
Alias           rjb -> Remove-Job
Alias           rm -> Remove-Item
Alias           rmdir -> Remove-Item
Alias           rmo -> Remove-Module
Alias           rni -> Rename-Item
Alias           rnp -> Rename-ItemProperty
Alias           rp -> Remove-ItemProperty
Alias           rsn -> Remove-PSSession
Alias           rv -> Remove-Variable
Alias           rvpa -> Resolve-Path
Alias           sajb -> Start-Job
Alias           sal -> Set-Alias
Alias           saps -> Start-Process
Alias           sasv -> Start-Service
Alias           sbp -> Set-PSBreakpoint
Alias           scb -> Set-Clipboard                               *******    Microsoft.PowerShell.Management
Alias           select -> Select-Object
Alias           set -> Set-Variable
Alias           shcm -> Show-Command
Alias           si -> Set-Item
Alias           sl -> Set-Location
Alias           sleep -> Start-Sleep
Alias           sls -> Select-String
Alias           sort -> Sort-Object
Alias           sp -> Set-ItemProperty
Alias           spjb -> Stop-Job
Alias           spps -> Stop-Process
Alias           spsv -> Stop-Service
Alias           start -> Start-Process
Alias           stz -> Set-TimeZone                                *******    Microsoft.PowerShell.Management
Alias           sv -> Set-Variable
Alias           tee -> Tee-Object
Alias           TNC -> Test-NetConnection                          *******    NetTCPIP
Alias           type -> Get-Content
Alias           where -> Where-Object
Alias           wjb -> Wait-Job
Alias           write -> Write-Output

PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
 removed as packages have dependencies for a reason. If
 you decide that you still want to remove it, head into
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:204:5
Line |
 204 |      Stop-OnError "Teste manual falhou"
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Teste manual falhou
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:204:5
Line |
 204 |      Stop-OnError "Teste manual falhou"
     |      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Teste manual falhou

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...

Did you know the proceeds of Pro (and some proceeds from other
 licensed editions) go into bettering the community infrastructure?
 Your support ensures an active community, keeps Chocolatey tip-top,
 plus it nets you some awesome features!
 https://chocolatey.org/compare
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:279:28
Line |
 279 |  … DE -ne 0) { Stop-OnError "Não foi possível verificar bases de dados"  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível verificar bases de dados
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:279:28
Line |
 279 |  … DE -ne 0) { Stop-OnError "Não foi possível verificar bases de dados"  …
     |                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Não foi possível verificar bases de dados

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK


Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:358:35
Line |
 358 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:358:35
Line |
 358 |  …  artisan migrate --force    || Stop-OnError "artisan migrate falhou."
     |                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ artisan migrate falhou.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8080:,https/*:8443:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...


📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB
   ➤ Password BookStack    : ZPH2LAB (para admin inicial)

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8080
   ➤ URL HTTPS     : https://localhost:8443

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8081, HTTPS=8444
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8081:,https/*:8444:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...


📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB
   ➤ Password BookStack    : ZPH2LAB (para admin inicial)

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8081
   ➤ URL HTTPS     : https://localhost:8444

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 `$env:ChocolateyInstall\lib` and find the package folder you want to
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8080:,https/*:8443:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...


📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB
   ➤ Password BookStack    : ZPH2LAB (para admin inicial)

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8080
   ➤ URL HTTPS     : https://localhost:8443

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
📍 Usando PHP CGI: C:\php\php-cgi.exe
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8081, HTTPS=8444
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8081:,https/*:8444:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...
🗑️ Removendo configurações FastCGI existentes...
➕ Adicionando configuração FastCGI...

Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:453:28
Line |
 453 |  …  ($LASTEXITCODE -ne 0) { Stop-OnError "Falha ao configurar FastCGI" }
     |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Falha ao configurar FastCGI
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:453:28
Line |
 453 |  …  ($LASTEXITCODE -ne 0) { Stop-OnError "Falha ao configurar FastCGI" }
     |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Falha ao configurar FastCGI

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
]633;D;1]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
📍 Usando PHP CGI: C:\php\php-cgi.exe
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK




🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8080:,https/*:8443:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...
🗑️ Removendo configurações FastCGI existentes...
➕ Adicionando configuração FastCGI...

➕ Adicionando handler PHP...

🔍 Verificando configuração FastCGI...
FastCGI configurado: <system.webServer>   <fastCgi>     <application fullPath="C:\php\php-cgi.exe">       <environmentVariables>       </environmentVariables>     </application>   </fastCgi> </system.webServer>
📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB
   ➤ Password BookStack    : ZPH2LAB (para admin inicial)

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8080
   ➤ URL HTTPS     : https://localhost:8443

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
📍 Usando PHP CGI: C:\php\php-cgi.exe
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB
[SC] DeleteService SUCCESS
 be removed. Then delete the folder for the package. You should use
 this option only as a last resort.
📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK


🎨 Gerando assets...


🌐 Configurando IIS...
🌐 Usando portas: HTTP=8081, HTTPS=8444
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8081:,https/*:8444:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...
🗑️ Removendo configurações FastCGI existentes...
➕ Adicionando configuração FastCGI...

➕ Adicionando handler PHP...

🔍 Verificando configuração FastCGI...
FastCGI configurado: <system.webServer>   <fastCgi>     <application fullPath="C:\php\php-cgi.exe">       <environmentVariables>       </environmentVariables>     </application>   </fastCgi> </system.webServer>
📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB
   ➤ Password BookStack    : ZPH2LAB (para admin inicial)

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8081
   ➤ URL HTTPS     : https://localhost:8444

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_curl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_curl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_gd.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_gd.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_intl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_intl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_mbstring.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_mbstring.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_openssl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_openssl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_zip.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_zip.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The directory is not empty. : 'C:\php\ext'
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The directory is not empty. : 'C:\php\ext'

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icudt72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icudt72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuin72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuin72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuio72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuio72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuuc72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuuc72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssh2.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssh2.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssl-3-x64.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssl-3-x64.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\nghttp2.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\nghttp2.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php-cgi.exe' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php-cgi.exe' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php8.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php8.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The process cannot access the file 'C:\php' because it is being used by another process.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The process cannot access the file 'C:\php' because it is being used by another process.

New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:41:1
Line |
  41 |  New-Item C:\php -ItemType Directory | Out-Null
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | An item with the specified name C:\php already exists.
New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:41:1
Line |
  41 |  New-Item C:\php -ItemType Directory | Out-Null
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | An item with the specified name C:\php already exists.

Remove-Item: Access to the path 'C:\php\ext\php_curl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_curl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_gd.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_gd.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_intl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_intl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_mbstring.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_mbstring.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_openssl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_openssl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_zip.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_zip.dll' is denied.

Remove-Item: Access to the path 'C:\php\icudt72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icudt72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuin72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuin72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuio72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuio72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuuc72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuuc72.dll' is denied.

Remove-Item: Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.
Remove-Item: Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.

Remove-Item: Access to the path 'C:\php\libssh2.dll' is denied.
Remove-Item: Access to the path 'C:\php\libssh2.dll' is denied.

Remove-Item: Access to the path 'C:\php\libssl-3-x64.dll' is denied.
Remove-Item: Access to the path 'C:\php\libssl-3-x64.dll' is denied.

Remove-Item: Access to the path 'C:\php\nghttp2.dll' is denied.
Remove-Item: Access to the path 'C:\php\nghttp2.dll' is denied.

Remove-Item: Access to the path 'C:\php\php-cgi.exe' is denied.
Remove-Item: Access to the path 'C:\php\php-cgi.exe' is denied.

Remove-Item: Access to the path 'C:\php\php8.dll' is denied.
Remove-Item: Access to the path 'C:\php\php8.dll' is denied.

📍 Usando PHP CGI: C:\php\php-cgi.exe
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK


🎨 Gerando assets...



✅ Ficheiro index.php encontrado e permissões definidas
🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8080:,https/*:8443:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...
🗑️ Removendo configurações FastCGI existentes...
➕ Adicionando configuração FastCGI...

➕ Adicionando handler PHP...

🔍 Verificando configuração FastCGI...
FastCGI configurado: <system.webServer>   <fastCgi>     <application fullPath="C:\php\php-cgi.exe">       <environmentVariables>       </environmentVariables>     </application>   </fastCgi> </system.webServer>
📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB
   ➤ Password BookStack    : ZPH2LAB (para admin inicial)

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8080
   ➤ URL HTTPS     : https://localhost:8443

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstackPS C:\inetpub\wwwroot\bookstack> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_curl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_curl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_gd.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_gd.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_intl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_intl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_mbstring.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_mbstring.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_openssl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_openssl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_zip.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_zip.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The directory is not empty. : 'C:\php\ext'
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The directory is not empty. : 'C:\php\ext'

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icudt72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icudt72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuin72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuin72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuio72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuio72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuuc72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuuc72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssh2.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssh2.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssl-3-x64.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssl-3-x64.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\nghttp2.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\nghttp2.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php-cgi.exe' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php-cgi.exe' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php8.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php8.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The process cannot access the file 'C:\php' because it is being used by another process.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The process cannot access the file 'C:\php' because it is being used by another process.

New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:41:1
Line |
  41 |  New-Item C:\php -ItemType Directory | Out-Null
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | An item with the specified name C:\php already exists.
New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:41:1
Line |
  41 |  New-Item C:\php -ItemType Directory | Out-Null
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | An item with the specified name C:\php already exists.

Remove-Item: Access to the path 'C:\php\ext\php_curl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_curl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_gd.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_gd.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_intl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_intl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_mbstring.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_mbstring.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_openssl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_openssl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_zip.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_zip.dll' is denied.

Remove-Item: Access to the path 'C:\php\icudt72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icudt72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuin72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuin72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuio72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuio72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuuc72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuuc72.dll' is denied.

Remove-Item: Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.
Remove-Item: Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.

Remove-Item: Access to the path 'C:\php\libssh2.dll' is denied.
Remove-Item: Access to the path 'C:\php\libssh2.dll' is denied.

Remove-Item: Access to the path 'C:\php\libssl-3-x64.dll' is denied.
Remove-Item: Access to the path 'C:\php\libssl-3-x64.dll' is denied.

Remove-Item: Access to the path 'C:\php\nghttp2.dll' is denied.
Remove-Item: Access to the path 'C:\php\nghttp2.dll' is denied.

Remove-Item: Access to the path 'C:\php\php-cgi.exe' is denied.
Remove-Item: Access to the path 'C:\php\php-cgi.exe' is denied.

Remove-Item: Access to the path 'C:\php\php8.dll' is denied.
Remove-Item: Access to the path 'C:\php\php8.dll' is denied.

📍 Usando PHP CGI: C:\php\php-cgi.exe
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 Verificando ficheiros essenciais...
✅ public\index.php encontrado
✅ artisan encontrado
✅ composer.json encontrado
✅ .env.example encontrado
📁 Conteúdo da pasta public:
   libs
   uploads
   .htaccess
   book_default_cover.png
   icon-128.png
   icon-180.png
   icon-32.png
   icon-64.png
   icon.ico
   icon.png
   index.php
   loading_error.png
   loading.gif
   logo.png
   user_avatar.png
   web.config
⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK


🎨 Gerando assets...



🧪 Testando execução do index.php...
Resultado do teste PHP: <!DOCTYPE html> <html>     <head>         <meta charset="UTF-8" />         <meta http-equiv="refresh" content="0;url='https://localhost/bookstack/login'" />          <title>Redirecting to https://localhost/bookstack/login</title>     </head>     <body>         Redirecting to <a href="https://localhost/bookstack/login">https://localhost/bookstack/login</a>.     </body> </html>
✅ Ficheiro index.php encontrado e permissões definidas
🌐 Configurando IIS...
🌐 Usando portas: HTTP=8081, HTTPS=8444
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8081:,https/*:8444:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...
🗑️ Removendo configurações FastCGI existentes...
➕ Adicionando configuração FastCGI...

➕ Adicionando handler PHP...

🔍 Verificando configuração FastCGI...
FastCGI configurado: <system.webServer>   <fastCgi>     <application fullPath="C:\php\php-cgi.exe">       <environmentVariables>       </environmentVariables>     </application>   </fastCgi> </system.webServer>
📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB
   ➤ Password BookStack    : ZPH2LAB (para admin inicial)

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8081
   ➤ URL HTTPS     : https://localhost:8444

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstack\x5cpublicPS C:\inetpub\wwwroot\bookstack\public> ]633;B
]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstack\x5cpublicPS C:\inetpub\wwwroot\bookstack\public> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_curl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_curl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_gd.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_gd.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_intl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_intl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_mbstring.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_mbstring.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_openssl.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_openssl.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_zip.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\ext\php_zip.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The directory is not empty. : 'C:\php\ext'
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The directory is not empty. : 'C:\php\ext'

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icudt72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icudt72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuin72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuin72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuio72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuio72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuuc72.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\icuuc72.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssh2.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssh2.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssl-3-x64.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\libssl-3-x64.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\nghttp2.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\nghttp2.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php-cgi.exe' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php-cgi.exe' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php8.dll' is denied.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Access to the path 'C:\php\php8.dll' is denied.

Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The process cannot access the file 'C:\php' because it is being used by another process.
Remove-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:40:25
Line |
  40 |  if (Test-Path C:\php) { Remove-Item C:\php -Recurse -Force }
     |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The process cannot access the file 'C:\php' because it is being used by another process.

New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:41:1
Line |
  41 |  New-Item C:\php -ItemType Directory | Out-Null
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | An item with the specified name C:\php already exists.
New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete5.ps1:41:1
Line |
  41 |  New-Item C:\php -ItemType Directory | Out-Null
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | An item with the specified name C:\php already exists.

Remove-Item: Access to the path 'C:\php\ext\php_curl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_curl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_fileinfo.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_gd.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_gd.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_intl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_intl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_mbstring.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_mbstring.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_openssl.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_openssl.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_pdo_mysql.dll' is denied.

Remove-Item: Access to the path 'C:\php\ext\php_zip.dll' is denied.
Remove-Item: Access to the path 'C:\php\ext\php_zip.dll' is denied.

Remove-Item: Access to the path 'C:\php\icudt72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icudt72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuin72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuin72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuio72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuio72.dll' is denied.

Remove-Item: Access to the path 'C:\php\icuuc72.dll' is denied.
Remove-Item: Access to the path 'C:\php\icuuc72.dll' is denied.

Remove-Item: Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.
Remove-Item: Access to the path 'C:\php\libcrypto-3-x64.dll' is denied.

Remove-Item: Access to the path 'C:\php\libssh2.dll' is denied.
Remove-Item: Access to the path 'C:\php\libssh2.dll' is denied.

Remove-Item: Access to the path 'C:\php\libssl-3-x64.dll' is denied.
Remove-Item: Access to the path 'C:\php\libssl-3-x64.dll' is denied.

Remove-Item: Access to the path 'C:\php\nghttp2.dll' is denied.
Remove-Item: Access to the path 'C:\php\nghttp2.dll' is denied.

Remove-Item: Access to the path 'C:\php\php-cgi.exe' is denied.
Remove-Item: Access to the path 'C:\php\php-cgi.exe' is denied.

Remove-Item: Access to the path 'C:\php\php8.dll' is denied.
Remove-Item: Access to the path 'C:\php\php8.dll' is denied.

📍 Usando PHP CGI: C:\php\php-cgi.exe
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
]633;D;1]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
⏹️ Parando IIS temporariamente...
WARNING: Waiting for service 'World Wide Web Publishing Service (W3SVC)' to stop...
🗑️ Removendo PHP antigo...
▶️ Reiniciando IIS...
📍 Usando PHP CGI: C:\php\php-cgi.exe
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 Verificando ficheiros essenciais...
✅ public\index.php encontrado
✅ artisan encontrado
✅ composer.json encontrado
✅ .env.example encontrado
📁 Conteúdo da pasta public:
   libs
   uploads
   .htaccess
   book_default_cover.png
   icon-128.png
   icon-180.png
   icon-32.png
   icon-64.png
   icon.ico
   icon.png
   index.php
   loading_error.png
   loading.gif
   logo.png
   user_avatar.png
   web.config
⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK


🎨 Gerando assets...



🧪 Testando execução do index.php...
Resultado do teste PHP: <!DOCTYPE html> <html>     <head>         <meta charset="UTF-8" />         <meta http-equiv="refresh" content="0;url='https://localhost/bookstack/login'" />          <title>Redirecting to https://localhost/bookstack/login</title>     </head>     <body>         Redirecting to <a href="https://localhost/bookstack/login">https://localhost/bookstack/login</a>.     </body> </html>
✅ Ficheiro index.php encontrado e permissões definidas
🔍 DIAGNÓSTICO COMPLETO:
========================
📁 Verificando estrutura BookStack:
✅ index.php existe
   Tamanho: 702 bytes

📄 Primeiras linhas do index.php:
   <?php

   use BookStack\Http\Request;
   use Illuminate\Contracts\Http\Kernel;


⚙️ Verificando .env:
✅ .env existe
   APP_KEY=base64:PzFYvc0BZgMy5EeP5Sanji5XiMNlrpKA3rzJz+Bl9zM=
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🐘 Teste direto PHP:
   PHP funciona: 8.3.24

🧪 Teste index.php via CLI:
   <!DOCTYPE html>
   <html>
       <head>
🌐 Configurando IIS...
🌐 Usando portas: HTTP=8080, HTTPS=8443
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8080:,https/*:8443:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...
🗑️ Removendo configurações FastCGI existentes...
➕ Adicionando configuração FastCGI...

➕ Adicionando handler PHP...

🔍 Verificando configuração FastCGI...
FastCGI configurado: <system.webServer>   <fastCgi>     <application fullPath="C:\php\php-cgi.exe">       <environmentVariables>       </environmentVariables>     </application>   </fastCgi> </system.webServer>
📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB
   ➤ Password BookStack    : ZPH2LAB (para admin inicial)

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8080
   ➤ URL HTTPS     : https://localhost:8443

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstack\x5cpublicPS C:\inetpub\wwwroot\bookstack\public> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>cls
]633;D;0]633;A]633;P;Cwd=C:\x5cGD\x5cDocs_Tecnicos\x5cWiki\x5cBookStack\x5cBookStackInstallerPackage-FinalPS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> ]633;B
PS>$global:__psEditorServices_userInput = $args[0]
PS>$global:__psEditorServices_userInput = ""
PS>.\Install-BookStack-Complete5.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
🔧 A instalar PHP…
⏹️ Parando IIS temporariamente...
WARNING: Waiting for service 'World Wide Web Publishing Service (W3SVC)' to stop...
🗑️ Removendo PHP antigo...
▶️ Reiniciando IIS...
📍 Usando PHP CGI: C:\php\php-cgi.exe
🔧 A instalar Composer…
🗑️ Limpeza completa do MariaDB...
Removendo serviço: MariaDB


📦 Instalação limpa do MariaDB...

✅ Binários encontrados em: C:\Program Files\MariaDB 11.8\bin
✅ mysql.exe encontrado!
✅ Usando MariaDB em: C:\Program Files\MariaDB 11.8
🗑️ Limpando datadir...
🔧 Inicializando base de dados...

⚙️ Configurando my.ini...
🧪 Testando arranque manual...
✅ Teste manual bem-sucedido!
🔧 Registando serviço...


🔄 Iniciando serviço MariaDB...
✅ Serviço MariaDB iniciado corretamente!
🔍 Localizando mysql.exe...
🔑 Testando conexão com password fornecida...
⚠️ Erro na conexão. Tentando acesso sem password...
🔄 Acesso sem password detectado. Definindo nova password...
✅ Password root configurada com sucesso!
🔧 Configurando base de dados...

📝 Password definida: ZPH2LAB
🔑 Criando utilizador bookstack_user...

🧪 Testando conexão...
✅ Utilizador criado e testado com sucesso!


🔍 Verificando ficheiros essenciais...
✅ public\index.php encontrado
✅ artisan encontrado
✅ composer.json encontrado
✅ .env.example encontrado
📁 Conteúdo da pasta public:
   libs
   uploads
   .htaccess
   book_default_cover.png
   icon-128.png
   icon-180.png
   icon-32.png
   icon-64.png
   icon.ico
   icon.png
   index.php
   loading_error.png
   loading.gif
   logo.png
   user_avatar.png
   web.config
⚠️ DB_PORT não encontrado no .env, adicionando...
📄 Configuração .env final:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307
🔍 VERIFICAÇÃO FINAL DA CONEXÃO:
================================
📄 Configuração .env:
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🔌 Teste final MySQL:
✅ MySQL: test MySQL OK

🐘 Teste final PHP:
✅ PHP: PHP OK

🔧 Testando conexão Laravel...
✅ Laravel: Conexão OK


🎨 Gerando assets...



🧪 Testando execução do index.php...
Resultado do teste PHP: <!DOCTYPE html> <html>     <head>         <meta charset="UTF-8" />         <meta http-equiv="refresh" content="0;url='https://localhost/bookstack/login'" />          <title>Redirecting to https://localhost/bookstack/login</title>     </head>     <body>         Redirecting to <a href="https://localhost/bookstack/login">https://localhost/bookstack/login</a>.     </body> </html>
✅ Ficheiro index.php encontrado e permissões definidas
🔍 DIAGNÓSTICO COMPLETO:
========================
📁 Verificando estrutura BookStack:
✅ index.php existe
   Tamanho: 702 bytes

📄 Primeiras linhas do index.php:
   <?php

   use BookStack\Http\Request;
   use Illuminate\Contracts\Http\Kernel;


⚙️ Verificando .env:
✅ .env existe
   APP_KEY=base64:4zzWvqD90wXmxMv9X1T2muByMQq+Ob+WUjc/Oo44Wow=
   APP_URL=https://localhost/bookstack
   DB_HOST=localhost
   DB_DATABASE=bookstack
   DB_USERNAME=bookstack_user
   DB_PASSWORD=ZPH2LAB
   DB_PORT=3307

🐘 Teste direto PHP:
   PHP funciona: 8.3.24

🧪 Teste index.php via CLI:
   <!DOCTYPE html>
   <html>
       <head>
🌐 Configurando IIS...
🌐 Usando portas: HTTP=8081, HTTPS=8444
🗑️ Removendo configurações IIS existentes...
📦 Criando App Pool...

🌐 Criando website...

✅ Website BookStack criado com sucesso!
   SITE "BookStack" (id:5,bindings:http/*:8081:,https/*:8444:,state:Started)
🔗 Associando App Pool ao site...

🔧 Configurando FastCGI para PHP...
🗑️ Removendo configurações FastCGI existentes...
➕ Adicionando configuração FastCGI...

➕ Adicionando handler PHP...

🔍 Verificando configuração FastCGI...
FastCGI configurado: <system.webServer>   <fastCgi>     <application fullPath="C:\php\php-cgi.exe">       <environmentVariables>       </environmentVariables>     </application>   </fastCgi> </system.webServer>
📄 Configurando documentos padrão...


🔒 Configurando SSL...
🔗 Configurando binding SSL...
▶️ Iniciando serviços IIS...


Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt

✅ Instalação concluída com sucesso!

🔐 Credenciais utilizadas:
   ➤ Password MariaDB root : ZPH2LAB
   ➤ Utilizador BD         : bookstack_user
   ➤ Password BD           : ZPH2LAB

🌐 URLs de acesso:
   ➤ URL HTTP      : http://localhost:8081
   ➤ URL HTTPS     : https://localhost:8444

🔑 LOGIN BOOKSTACK:
   ➤ Email         : <EMAIL>
   ➤ Password      : password

📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager
]633;D;0]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstack\x5cpublicPS C:\inetpub\wwwroot\bookstack\public> ]633;B
]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstack\x5cpublicPS C:\inetpub\wwwroot\bookstack\public> ]633;B
]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstack\x5cpublicPS C:\inetpub\wwwroot\bookstack\public> ]633;B
]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstack\x5cpublicPS C:\inetpub\wwwroot\bookstack\public> ]633;B
]633;A]633;P;Cwd=C:\x5cinetpub\x5cwwwroot\x5cbookstack\x5cpublicPS C:\inetpub\wwwroot\bookstack\public> ]633;B
