
<#  Install‑BookStack‑Complete.ps1
    Instala BookStack + MariaDB + IIS (HTTPS) num Windows Server / Windows 10+
#>

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append
function Stop-OnError ($msg) { Write-Error "❌ $msg"; Stop-Transcript; exit 1 }

# 1. PASSWORDS
$secureRootPassword = Read-Host "Password ROOT actual do MariaDB (novo se for instalar)" -AsSecureString
$rootPassword       = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
                         [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword))
Stop-Transcript
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | % { [char]$_ })
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# 2. CHOCOLATEY & URL Rewrite
if (-not (Get-Command choco.exe -EA SilentlyContinue)) {
    Write-Host "📦 A instalar Chocolatey…"
    Set-ExecutionPolicy Bypass -Scope Process -Force
    iex ((New-Object Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}
if (-not (Test-Path "$env:SystemRoot\System32\inetsrv\rewrite.dll")) {
    choco install urlrewrite -y                || Stop-OnError "URL Rewrite falhou"
}
choco install vcredist140 -y --ignore-checksums | Out-Null

# 3. PHP
Write-Host "🔧 A instalar PHP…"

# Parar IIS primeiro para libertar ficheiros PHP
Write-Host "⏹️ Parando IIS temporariamente..."
Stop-Service W3SVC -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

# Tentar remover PHP com mais força
if (Test-Path C:\php) { 
    Write-Host "🗑️ Removendo PHP antigo..."
    try {
        Remove-Item C:\php -Recurse -Force -ErrorAction Stop
    } catch {
        Write-Host "⚠️ Alguns ficheiros PHP estão em uso, continuando..."
    }
}

$phpPage = Invoke-WebRequest "https://windows.php.net/downloads/releases/" -UseBasicParsing
$phpLink = ($phpPage.Links |
            Where-Object { $_.href -match '-nts-Win32-vs16-x64\.zip$' -and $_.href -notmatch '(devel|debug)' } |
            Sort-Object href -Descending |
            Select-Object -First 1).href
if (-not $phpLink) { Stop-OnError "Nenhum build de release x64 encontrado." }
$phpZipUrl = "https://windows.php.net$phpLink"
$phpZip    = "$env:TEMP\php.zip"
Invoke-WebRequest $phpZipUrl -OutFile $phpZip -EA Stop

if (-not (Test-Path C:\php)) { New-Item C:\php -ItemType Directory | Out-Null }
Expand-Archive $phpZip -DestinationPath C:\php -Force

# Reiniciar IIS
Write-Host "▶️ Reiniciando IIS..."
Start-Service W3SVC

$phpRoot = (Get-ChildItem C:\php -Recurse -Filter php.exe | Select-Object -First 1).Directory.FullName
if (-not $phpRoot) { Stop-OnError "Falha ao localizar php.exe." }

# Verificar se php-cgi.exe existe
$phpCgi = Join-Path $phpRoot 'php-cgi.exe'
if (-not (Test-Path $phpCgi)) {
    Write-Host "⚠️ php-cgi.exe não encontrado. A usar php.exe como alternativa."
    $phpCgi = Join-Path $phpRoot 'php.exe'
}

Write-Host "📍 Usando PHP CGI: $phpCgi"

$phpIni  = Join-Path $phpRoot 'php.ini'
$iniProd = Join-Path $phpRoot 'php.ini-production'
$iniDev  = Join-Path $phpRoot 'php.ini-development'
if     (Test-Path $iniProd) { Copy-Item $iniProd $phpIni -Force }
elseif (Test-Path $iniDev)  { Copy-Item $iniDev  $phpIni -Force }
else   { New-Item $phpIni -ItemType File | Out-Null }

$ext = 'mbstring','openssl','simplexml','gd','fileinfo','curl','pdo_mysql','intl','zip'
(Get-Content $phpIni) |
  ForEach-Object {
      if    ($_ -match "^\s*;\s*extension=($($ext -join '|'))") { $_.TrimStart(';') }
      elseif($_ -match '^;?date\.timezone')     { 'date.timezone = Europe/Lisbon' }
      elseif($_ -match '^;?upload_max_filesize') { 'upload_max_filesize = 50M' }
      else { $_ }
  } | Set-Content $phpIni

[Environment]::SetEnvironmentVariable('Path', "$phpRoot;$env:Path", 'Machine')
$env:Path = "$phpRoot;$env:Path"

# 4. COMPOSER
Write-Host "🔧 A instalar Composer…"
$composerSetup = "$env:TEMP\ComposerSetup-$PID.exe"
Invoke-WebRequest https://getcomposer.org/Composer-Setup.exe -OutFile $composerSetup -EA Stop
Start-Process $composerSetup -ArgumentList '/SILENT', '/ALLUSERS=1', '/NORESTART' -Wait

$composer = Get-Command composer -EA SilentlyContinue |
            Select-Object -First 1 -ExpandProperty Source
if (-not $composer) {
    $composer = Get-ChildItem "$Env:ProgramData\ComposerSetup","$Env:AppData\ComposerSetup" -Recurse -Filter composer.* |
                Where-Object { $_.Name -match '^composer\.(bat|cmd)$' } |
                Select-Object -First 1 -Expand FullName
}
if (-not $composer) { Stop-OnError "Composer não ficou acessível." }

# 5. MARIADB
function Test-RootLogin ([string]$Pwd) {
    $mysqlCmd = Get-Command mysql.exe -EA SilentlyContinue | Select -First 1 -Expand Source
    if ($mysqlCmd) { & $mysqlCmd -u root --password="$Pwd" -e "SELECT 1;" 2>$null }
    return $LASTEXITCODE -eq 0
}

Write-Host "🗑️ Limpeza completa do MariaDB..."

# Parar serviços
Get-Service -Name "*MariaDB*" -ErrorAction SilentlyContinue | Stop-Service -Force -ErrorAction SilentlyContinue

# Remover serviços
$services = Get-WmiObject -Class Win32_Service | Where-Object { $_.Name -like "*MariaDB*" }
foreach ($service in $services) {
    Write-Host "Removendo serviço: $($service.Name)"
    & sc.exe delete $service.Name
}

# Forçar remoção dos pacotes Chocolatey
choco uninstall mariadb mariadb.install -y --force --remove-dependencies --skip-autouninstaller -n

# Remover pastas manualmente
$foldersToRemove = @(
    "C:\Program Files\MariaDB*",
    "C:\ProgramData\MySQL",
    "C:\ProgramData\MariaDB",
    "$Env:ProgramData\chocolatey\lib\mariadb*"
)

foreach ($folder in $foldersToRemove) {
    Get-ChildItem $folder -ErrorAction SilentlyContinue | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "📦 Instalação limpa do MariaDB..."
choco install mariadb.install -y --force --params "/rootpwd=`"$rootPassword`"" 
if ($LASTEXITCODE -ne 0) { Stop-OnError "Falha na instalação do MariaDB" }
Start-Sleep -Seconds 20

# Verificar se os binários foram instalados
$binPath = "C:\Program Files\MariaDB*\bin"
$binFolder = Get-ChildItem $binPath -ErrorAction SilentlyContinue | Select-Object -First 1
if ($binFolder) {
    Write-Host "✅ Binários encontrados em: $($binFolder.FullName)"
    $mysqlExe = Join-Path $binFolder.FullName "mysql.exe"
    $mysqldExe = Join-Path $binFolder.FullName "mysqld.exe"
    if (Test-Path $mysqlExe) {
        Write-Host "✅ mysql.exe encontrado!"
        $env:PATH = "$($binFolder.FullName);$env:PATH"
    } else {
        Stop-OnError "mysql.exe não encontrado na pasta bin"
    }
} else {
    Stop-OnError "Pasta bin do MariaDB não encontrada após instalação"
}

# Definir caminhos dinâmicos baseados na versão instalada
$mariadbBase = Get-ChildItem "C:\Program Files\MariaDB*" -Directory | Select-Object -First 1
if (-not $mariadbBase) { Stop-OnError "Pasta MariaDB não encontrada após instalação" }

$binFolder = Join-Path $mariadbBase.FullName "bin"
$dataDir = Join-Path $mariadbBase.FullName "data"
$myIniPath = Join-Path $dataDir "my.ini"
$mysqlExe = Join-Path $binFolder "mysql.exe"
$mysqldExe = Join-Path $binFolder "mysqld.exe"
$installDbExe = Join-Path $binFolder "mysql_install_db.exe"

Write-Host "✅ Usando MariaDB em: $($mariadbBase.FullName)"

# 1. Limpar datadir completamente
Write-Host "🗑️ Limpando datadir..."
if (Test-Path $dataDir) {
    Remove-Item "$dataDir\*" -Force -Recurse -ErrorAction SilentlyContinue
}

# 2. Inicializar base de dados
Write-Host "🔧 Inicializando base de dados..."
& $installDbExe --datadir="$dataDir"
if ($LASTEXITCODE -ne 0) { Stop-OnError "Falha na inicialização da base de dados" }

# 3. Adicionar port=3307 ao my.ini
Write-Host "⚙️ Configurando my.ini..."
if (Test-Path $myIniPath) {
    $iniContent = Get-Content $myIniPath
    if ($iniContent -notmatch "port\s*=") {
        # Adicionar port na secção [mysqld]
        $newContent = @()
        foreach ($line in $iniContent) {
            $newContent += $line
            # Adicionar port imediatamente após encontrar [mysqld]
            if ($line -match "^\s*\[mysqld\]") {
                $newContent += "port=3307"
            }
        }
        Set-Content $myIniPath $newContent
    }
}

# 4. Testar arranque manual primeiro
Write-Host "🧪 Testando arranque manual..."
$testProcess = Start-Process $mysqldExe -ArgumentList "--defaults-file=`"$myIniPath`"", "--console" -PassThru -WindowStyle Hidden
Start-Sleep -Seconds 10

if (-not $testProcess.HasExited) {
    Write-Host "✅ Teste manual bem-sucedido!"
    $testProcess.Kill()
    Start-Sleep -Seconds 3
} else {
    Stop-OnError "Teste manual falhou"
}

# 5. Registar e configurar serviço
Write-Host "🔧 Registando serviço..."
& $mysqldExe --install MariaDB
Start-Sleep -Seconds 3

# 6. Configurar serviço para usar my.ini
$escapedExe = '"{0}"' -f $mysqldExe
$escapedIni = '"{0}"' -f $myIniPath
$binPath = "$escapedExe --defaults-file=$escapedIni MariaDB"
sc.exe config MariaDB binPath= $binPath

# 7. Iniciar serviço
Write-Host "🔄 Iniciando serviço MariaDB..."
Start-Service MariaDB -ErrorAction Stop
Start-Sleep -Seconds 10

$svc = Get-Service MariaDB
if ($svc.Status -eq 'Running') {
    Write-Host "✅ Serviço MariaDB iniciado corretamente!"
} else {
    Stop-OnError "❌ Serviço MariaDB não arrancou (Estado: $($svc.Status))"
}

# Adicionar binários ao PATH
$env:PATH = "$binFolder;$env:PATH"

# Localizar mysql.exe com busca abrangente
Write-Host "🔍 Localizando mysql.exe..."
$mysql = Get-Command mysql.exe -ErrorAction SilentlyContinue | Select-Object -First 1 -ExpandProperty Source

if (-not $mysql) {
    $mysql = Get-ChildItem "C:\Program Files\MariaDB*" -Recurse -Filter mysql.exe -ErrorAction SilentlyContinue |
             Select-Object -First 1 -ExpandProperty FullName
    if ($mysql) {
        $mysqlDir = Split-Path $mysql
        $env:PATH = "$mysqlDir;$env:PATH"
    }
}

if (-not $mysql) {
    $mysql = Get-ChildItem "$Env:ProgramData\chocolatey\lib\mariadb*" -Recurse -Filter mysql.exe -ErrorAction SilentlyContinue |
             Select-Object -First 1 -ExpandProperty FullName
    if ($mysql) {
        $mysqlDir = Split-Path $mysql
        $env:PATH = "$mysqlDir;$env:PATH"
    }
}

if (-not $mysql) { Stop-OnError "mysql.exe não encontrado." }

# Testar conexão e configurar password
Write-Host "🔑 Testando conexão com password fornecida..."
$testResult = & $mysqlExe -u root --password="$rootPassword" --port=3307 -e "SELECT 1 AS test;" 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Conexão bem-sucedida com a password fornecida!"
} else {
    Write-Host "⚠️ Erro na conexão. Tentando acesso sem password..."
    
    $testNoPass = & $mysqlExe -u root --port=3307 -e "SELECT 1 AS test;" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "🔄 Acesso sem password detectado. Definindo nova password..."
        $setPassResult = & $mysqlExe -u root --port=3307 -e "SET PASSWORD FOR 'root'@'localhost' = PASSWORD('$rootPassword'); FLUSH PRIVILEGES;" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Password root configurada com sucesso!"
        } else {
            Stop-OnError "Falha ao configurar password root"
        }
    } else {
        Stop-OnError "Não foi possível aceder ao MariaDB"
    }
}

# Configurar base de dados BookStack
Write-Host "🔧 Configurando base de dados..."

# Primeiro, verificar se a BD existe
$dbExists = & $mysql -u root --password="$rootPassword" --port=3307 -e "SHOW DATABASES LIKE 'bookstack';" 2>$null
if ($LASTEXITCODE -ne 0) { Stop-OnError "Não foi possível verificar bases de dados" }

# Criar BD se não existir
if (-not $dbExists) {
    & $mysql -u root --password="$rootPassword" --port=3307 -e "CREATE DATABASE bookstack CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" || Stop-OnError "Criação da BD falhou"
}

# Gerar password fixa
$bookstackUserPassword = "ZPH2LAB"
Write-Host "📝 Password definida: $bookstackUserPassword"

# Remover utilizador existente e criar novo
$userSql = @"
DROP USER IF EXISTS 'bookstack_user'@'localhost';
CREATE USER 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@

Write-Host "🔑 Criando utilizador bookstack_user..."
& $mysql -u root --password="$rootPassword" --port=3307 -e "$userSql" || Stop-OnError "Criação do utilizador falhou"

# Testar imediatamente
Write-Host "🧪 Testando conexão..."
$testConnection = & $mysql -u bookstack_user --password="$bookstackUserPassword" --port=3307 -D bookstack -e "SELECT 'Conexão OK' AS status;" 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Utilizador criado e testado com sucesso!"
} else {
    Write-Host "❌ Erro no teste: $testConnection"
    Stop-OnError "Utilizador não funciona após criação"
}

# 6. BOOKSTACK
$bookstackDir = "C:\inetpub\wwwroot\bookstack"
if (Test-Path $bookstackDir) { Remove-Item $bookstackDir -Recurse -Force }
git clone https://github.com/BookStackApp/BookStack.git $bookstackDir || Stop-OnError "Clone BookStack falhou."

Set-Location $bookstackDir
& $composer install --no-dev --no-interaction || Stop-OnError "Composer falhou."

# Verificar se os ficheiros essenciais existem
Write-Host "🔍 Verificando ficheiros essenciais..."
$essentialFiles = @(
    "public\index.php",
    "artisan",
    "composer.json",
    ".env.example"
)

foreach ($file in $essentialFiles) {
    $fullPath = Join-Path $bookstackDir $file
    if (Test-Path $fullPath) {
        Write-Host "✅ $file encontrado"
    } else {
        Stop-OnError "$file não encontrado em $bookstackDir"
    }
}

# Verificar conteúdo da pasta public
Write-Host "📁 Conteúdo da pasta public:"
Get-ChildItem "$bookstackDir\public" | ForEach-Object { Write-Host "   $($_.Name)" }

Copy-Item .env.example .env -Force
(Get-Content .env) |
  ForEach-Object {
      $_ -replace '^APP_URL=.*',    'APP_URL=https://localhost/bookstack' `
         -replace '^DB_HOST=.*',    'DB_HOST=localhost' `
         -replace '^DB_PORT=.*',    'DB_PORT=3307' `
         -replace '^DB_DATABASE=.*','DB_DATABASE=bookstack' `
         -replace '^DB_USERNAME=.*','DB_USERNAME=bookstack_user' `
         -replace '^DB_PASSWORD=.*',"DB_PASSWORD=$bookstackUserPassword"
  } | Set-Content .env

# Verificar se DB_PORT foi adicionado corretamente
$envContent = Get-Content .env
if ($envContent -notmatch "^DB_PORT=") {
    Write-Host "⚠️ DB_PORT não encontrado no .env, adicionando..."
    $envContent += "DB_PORT=3307"
    Set-Content .env $envContent
}

# Mostrar configuração final
Write-Host "📄 Configuração .env final:"
Get-Content .env | Where-Object { $_ -match "^(DB_|APP_URL)" } | ForEach-Object { Write-Host "   $_" }

# DIAGNÓSTICO SIMPLIFICADO
Write-Host "🔍 VERIFICAÇÃO FINAL DA CONEXÃO:"
Write-Host "================================"

# Mostrar configuração
Write-Host "📄 Configuração .env:"
Get-Content .env | Where-Object { $_ -match "^(DB_|APP_URL)" } | ForEach-Object { Write-Host "   $_" }

# Teste final simples
Write-Host "`n🔌 Teste final MySQL:"
$finalTest = & $mysql -u bookstack_user --password="$bookstackUserPassword" --port=3307 -D bookstack -e "SELECT 'MySQL OK' AS test;" 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ MySQL: $finalTest"
} else {
    Write-Host "❌ MySQL: $finalTest"
    Stop-OnError "MySQL ainda não funciona"
}

Write-Host "`n🐘 Teste final PHP:"
$phpFinalTest = php -r "try { `$pdo = new PDO('mysql:host=localhost;port=3307;dbname=bookstack;charset=utf8mb4', 'bookstack_user', '$bookstackUserPassword'); echo 'PHP OK'; } catch(Exception `$e) { echo 'ERRO: ' . `$e->getMessage(); exit(1); }" 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PHP: $phpFinalTest"
} else {
    Write-Host "❌ PHP: $phpFinalTest"
    Stop-OnError "PHP ainda não funciona"
}

# Teste adicional: verificar se Laravel consegue conectar
Write-Host "`n🔧 Testando conexão Laravel..."
$laravelTest = php artisan tinker --execute="DB::connection()->getPdo(); echo 'Laravel DB OK';" 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Laravel: Conexão OK"
} else {
    Write-Host "❌ Laravel: $laravelTest"
    Write-Host "🔍 Verificando .env novamente..."
    Get-Content .env | Where-Object { $_ -match "^DB_" }
    Stop-OnError "Laravel não consegue conectar à base de dados"
}

php artisan key:generate       || Stop-OnError "artisan key:generate falhou."
php artisan migrate --force    || Stop-OnError "artisan migrate falhou."

# Gerar assets e cache
Write-Host "🎨 Gerando assets..."
php artisan config:clear 2>$null
php artisan config:cache 2>$null
php artisan route:cache 2>$null
php artisan view:cache 2>$null

# 7. PERMISSÕES
icacls "$bookstackDir\storage"          /grant 'IIS_IUSRS:(OI)(CI)(M)' /T
icacls "$bookstackDir\bootstrap\cache" /grant 'IIS_IUSRS:(OI)(CI)(M)' /T
icacls "$bookstackDir\public"          /grant 'IIS_IUSRS:(OI)(CI)(R)' /T

# Verificar se index.php existe
if (-not (Test-Path "$bookstackDir\public\index.php")) {
    Stop-OnError "index.php não encontrado em $bookstackDir\public\"
}

# Testar se o PHP consegue executar o index.php
Write-Host "🧪 Testando execução do index.php..."
Set-Location "$bookstackDir\public"
$phpTest = php -f index.php 2>&1
Write-Host "Resultado do teste PHP: $phpTest"

Write-Host "✅ Ficheiro index.php encontrado e permissões definidas"

# Diagnóstico completo antes de configurar IIS
Write-Host "🔍 DIAGNÓSTICO COMPLETO:"
Write-Host "========================"

# 1. Verificar se BookStack foi clonado corretamente
Write-Host "📁 Verificando estrutura BookStack:"
if (Test-Path "$bookstackDir\public\index.php") {
    Write-Host "✅ index.php existe"
    $indexSize = (Get-Item "$bookstackDir\public\index.php").Length
    Write-Host "   Tamanho: $indexSize bytes"
} else {
    Write-Host "❌ index.php NÃO existe"
}

# 2. Verificar conteúdo do index.php
Write-Host "`n📄 Primeiras linhas do index.php:"
if (Test-Path "$bookstackDir\public\index.php") {
    Get-Content "$bookstackDir\public\index.php" -TotalCount 5 | ForEach-Object { Write-Host "   $_" }
}

# 3. Verificar se .env existe e está configurado
Write-Host "`n⚙️ Verificando .env:"
if (Test-Path "$bookstackDir\.env") {
    Write-Host "✅ .env existe"
    Get-Content "$bookstackDir\.env" | Where-Object { $_ -match "^(APP_|DB_)" } | ForEach-Object { Write-Host "   $_" }
} else {
    Write-Host "❌ .env NÃO existe"
}

# 4. Teste direto do PHP
Write-Host "`n🐘 Teste direto PHP:"
Set-Location "$bookstackDir\public"
$phpDirectTest = php -r "echo 'PHP funciona: ' . phpversion();" 2>&1
Write-Host "   $phpDirectTest"

# 5. Teste do index.php via CLI
Write-Host "`n🧪 Teste index.php via CLI:"
$indexTest = php -f index.php 2>&1 | Select-Object -First 3
$indexTest | ForEach-Object { Write-Host "   $_" }

# 8. IIS + HTTPS
Write-Host "🌐 Configurando IIS..."

# Encontrar porta disponível
$httpPort = 8080
$httpsPort = 8443
while ((Get-NetTCPConnection -LocalPort $httpPort -EA SilentlyContinue)) {
    $httpPort++
}
while ((Get-NetTCPConnection -LocalPort $httpsPort -EA SilentlyContinue)) {
    $httpsPort++
}

Write-Host "🌐 Usando portas: HTTP=$httpPort, HTTPS=$httpsPort"

# Atualizar .env com a porta correta
(Get-Content "$bookstackDir\.env") |
  ForEach-Object {
      $_ -replace '^APP_URL=.*', "APP_URL=https://localhost:${httpsPort}"
  } | Set-Content "$bookstackDir\.env"

# Remover App Pool e Site existentes
Write-Host "🗑️ Removendo configurações IIS existentes..."
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" delete site BookStack 2>$null
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" delete apppool BookStackAppPool 2>$null

# Criar App Pool
Write-Host "📦 Criando App Pool..."
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" add apppool /name:BookStackAppPool /managedRuntimeVersion: /processModel.identityType:ApplicationPoolIdentity

# Criar site
Write-Host "🌐 Criando website..."
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" add site /name:BookStack /physicalPath:"$bookstackDir\public" /bindings:"http/*:${httpPort}:,https/*:${httpsPort}:"

# Verificar se o site foi criado
$siteCheck = & "$env:SystemRoot\System32\inetsrv\appcmd.exe" list site BookStack 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Website BookStack criado com sucesso!"
    Write-Host "   $siteCheck"
    
    # Configurar App Pool para o site
    Write-Host "🔗 Associando App Pool ao site..."
    & "$env:SystemRoot\System32\inetsrv\appcmd.exe" set app "BookStack/" /applicationPool:BookStackAppPool
    
    # Verificar e corrigir caminho físico
    Write-Host "📁 Verificando caminho físico..."
    & "$env:SystemRoot\System32\inetsrv\appcmd.exe" set vdir "BookStack/" /physicalPath:"$bookstackDir\public"
} else {
    Stop-OnError "Falha ao criar website no IIS"
}

# Configurar FastCGI para PHP
Write-Host "🔧 Configurando FastCGI para PHP..."

# Verificar se o executável PHP existe
if (-not (Test-Path $phpCgi)) {
    Stop-OnError "PHP CGI não encontrado em: $phpCgi"
}

# Remover configurações existentes primeiro
Write-Host "🗑️ Removendo configurações FastCGI existentes..."
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" set config /section:system.webServer/handlers /-"[name='PHP_via_FastCGI']" 2>$null
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" set config /section:system.webServer/fastCgi /-"[fullPath='$phpCgi']" 2>$null

# Adicionar configurações limpas
Write-Host "➕ Adicionando configuração FastCGI..."
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" set config /section:system.webServer/fastCgi /+"[fullPath='$phpCgi']"
if ($LASTEXITCODE -ne 0) { Stop-OnError "Falha ao configurar FastCGI" }

Write-Host "➕ Adicionando handler PHP..."
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" set config /section:system.webServer/handlers /+"[name='PHP_via_FastCGI',path='*.php',verb='*',modules='FastCgiModule',scriptProcessor='$phpCgi',resourceType='Either']"
if ($LASTEXITCODE -ne 0) { Stop-OnError "Falha ao configurar handler PHP" }

# Verificar configuração
Write-Host "🔍 Verificando configuração FastCGI..."
$fastCgiCheck = & "$env:SystemRoot\System32\inetsrv\appcmd.exe" list config /section:fastCgi
Write-Host "FastCGI configurado: $fastCgiCheck"

# Configurar default document
Write-Host "📄 Configurando documentos padrão..."
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" set config "BookStack" /section:defaultDocument /enabled:true
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" set config "BookStack" /section:defaultDocument /+files.[value='index.php']

# Certificado SSL
Write-Host "🔒 Configurando SSL..."
$cert = Get-ChildItem Cert:\LocalMachine\My | ? { $_.Subject -eq 'CN=localhost' } | Select -First 1
if (-not $cert) { 
    Write-Host "📜 Criando certificado SSL..."
    $cert = New-SelfSignedCertificate -DnsName localhost -CertStoreLocation Cert:\LocalMachine\My 
}

# Binding SSL
Write-Host "🔗 Configurando binding SSL..."
netsh http add sslcert ipport=0.0.0.0:$httpsPort certhash=$($cert.Thumbprint) appid="{00000000-0000-0000-0000-000000000000}" 2>$null

# Iniciar App Pool e Site
Write-Host "▶️ Iniciando serviços IIS..."
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" start apppool BookStackAppPool
& "$env:SystemRoot\System32\inetsrv\appcmd.exe" start site BookStack

# web.config para Laravel
Set-Content "$bookstackDir\public\web.config" @"
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="index.php" />
      </files>
    </defaultDocument>
    <staticContent>
      <remove fileExtension=".css" />
      <remove fileExtension=".js" />
      <mimeMap fileExtension=".css" mimeType="text/css" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
    </staticContent>
    <rewrite>
      <rules>
        <rule name="Static Files" stopProcessing="true">
          <match url="^(css|js|images|fonts|dist|libs)/.*" />
          <action type="None" />
        </rule>
        <rule name="Imported Rule 1" stopProcessing="true">
          <match url="^" ignoreCase="false" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
          </conditions>
          <action type="Rewrite" url="index.php" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
"@


# 9. DONE
Stop-Transcript
Write-Host "`n✅ Instalação concluída com sucesso!"
Write-Host "`n🔐 Credenciais utilizadas:"
Write-Host "   ➤ Password MariaDB root : $rootPassword"
Write-Host "   ➤ Utilizador BD         : bookstack_user"
Write-Host "   ➤ Password BD           : ZPH2LAB"
Write-Host "`n🌐 URLs de acesso:"
Write-Host "   ➤ URL HTTP      : http://localhost:$httpPort"
Write-Host "   ➤ URL HTTPS     : https://localhost:$httpsPort"
Write-Host "`n🔑 LOGIN BOOKSTACK:"
Write-Host "   ➤ Email         : <EMAIL>"
Write-Host "   ➤ Password      : password"
Write-Host "`n📝 Nota: Se HTTPS não funcionar, configurar SSL manualmente no IIS Manager`n"

















































