**********************
PowerShell transcript start
Start time: 20250728212331
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 23728
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728212335
**********************
**********************
PowerShell transcript start
Start time: 20250728212335
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 23728
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:48
Line |
  48 |  Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini-development' because it does not exist.
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:48
Line |
  48 |  Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini-development' because it does not exist.

Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:49
Line |
  49 |  (gc $phpIni) | % {
     |   ~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini' because it does not exist.
Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:49
Line |
  49 |  (gc $phpIni) | % {
     |   ~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini' because it does not exist.

Chocolatey v1.1.0
Installing the following packages:
mariadb
By installing, you accept licenses for the packages.
Progress: Downloading mariadb.install 11.8.2... 100%
Progress: Downloading mariadb 11.8.2... 100%

mariadb.install v11.8.2 [Approved]
mariadb.install package files install completed. Performing other installation steps.
Installing 64-bit mariadb.install...
mariadb.install has been installed.
PATH environment variable does not have C:\Program Files\MariaDB 11.8\bin in it. Adding...
The requested service has already been started.

More help is available by typing NET HELPMSG 2182.

  mariadb.install may be able to be automatically uninstalled.
Environment Vars (like PATH) have changed. Close/reopen your shell to
 see the changes (or in powershell/cmd.exe just type `refreshenv`).
 The install of mariadb.install was successful.
  Software installed to 'C:\Program Files\MariaDB 11.8\'

mariadb v11.8.2 [Approved]
mariadb package files install completed. Performing other installation steps.
 The install of mariadb was successful.
  Software installed to 'C:\ProgramData\chocolatey\lib\mariadb'

Chocolatey installed 2/2 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).
PS>TerminatingError(Get-Command): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The term 'mysql.exe' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again."
>> TerminatingError(Get-Command): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The term 'mysql.exe' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again."
>> TerminatingError(Get-Command): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The term 'mysql.exe' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again."
The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The term 'mysql.exe' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
Get-Command: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:69
Line |
  69 |  $mysql = Get-Command mysql.exe -ErrorAction Stop | Select-Object -Exp …
     |           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The term 'mysql.exe' is not recognized as a name of a cmdlet, function, script file, or executable program.
     | Check the spelling of the name, or if a path was included, verify that the path is correct and try again.

