**********************
PowerShell transcript start
Start time: 20250728213554
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 17516
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
**********************
PowerShell transcript end
End time: 20250728213600
**********************
**********************
PowerShell transcript start
Start time: 20250728213600
Username: QUIDGEST\hgeraldes
RunAs User: QUIDGEST\hgeraldes
Configuration Name: 
Machine: PROCYON (Microsoft Windows NT 10.0.26100.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -WorkingDirectory ~
Process ID: 17516
PSVersion: 7.4.7
PSEdition: Core
GitCommitId: 7.4.7
OS: Microsoft Windows 10.0.26100
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:37
Line |
  37 |  Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini-development' because it does not exist.
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:37
Line |
  37 |  Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini-development' because it does not exist.

Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:38
Line |
  38 |  (gc $phpIni) | % {
     |   ~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini' because it does not exist.
Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:38
Line |
  38 |  (gc $phpIni) | % {
     |   ~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini' because it does not exist.

PS>TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> .\Install-BookStack-Complete.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Move-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:40
Line |
  40 |  Move-Item $extracted.FullName C:\php -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot create 'C:\php\php-8.3.23-devel-vs16-x64' because a file or directory with the same name already exists.
Move-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:40
Line |
  40 |  Move-Item $extracted.FullName C:\php -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot create 'C:\php\php-8.3.23-devel-vs16-x64' because a file or directory with the same name already exists.

Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:45
Line |
  45 |  Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini-development' because it does not exist.
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:45
Line |
  45 |  Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini-development' because it does not exist.

Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:46
Line |
  46 |  (gc $phpIni) | % {
     |   ~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini' because it does not exist.
Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:46
Line |
  46 |  (gc $phpIni) | % {
     |   ~~~~~~~~~~
     | Cannot find path 'C:\php\php.ini' because it does not exist.

PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> TerminatingError(Invoke-WebRequest): "The process cannot access the file 'C:\Users\<USER>\AppData\Local\Temp\ComposerSetup.exe' because it is being used by another process."
Invoke-WebRequest: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:55
Line |
  55 |  Invoke-WebRequest "https://getcomposer.org/Composer-Setup.exe" -OutFi …
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The process cannot access the file 'C:\Users\<USER>\AppData\Local\Temp\ComposerSetup.exe' because it is being used by another process.
Invoke-WebRequest: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:55
Line |
  55 |  Invoke-WebRequest "https://getcomposer.org/Composer-Setup.exe" -OutFi …
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | The process cannot access the file 'C:\Users\<USER>\AppData\Local\Temp\ComposerSetup.exe' because it is being
     | used by another process.

Chocolatey v1.1.0
Installing the following packages:
mariadb
By installing, you accept licenses for the packages.
mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - mariadb - mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
fatal: destination path 'C:\inetpub\wwwroot\bookstack' already exists and is not an empty directory.
&: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:78
Line |
  78 |  & "$composer" install --no-dev
     |    ~~~~~~~~~~~
     | The term 'C:\Program Files\ComposerSetup\bin\composer.bat' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
&: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:78
Line |
  78 |  & "$composer" install --no-dev
     |    ~~~~~~~~~~~
     | The term 'C:\Program Files\ComposerSetup\bin\composer.bat' is not recognized as a name of a cmdlet, function,
     | script file, or executable program. Check the spelling of the name, or if a path was included, verify that the
     | path is correct and try again.

php: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:88
Line |
  88 |  php artisan key:generate
     |  ~~~
     | The term 'php' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
php: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:88
Line |
  88 |  php artisan key:generate
     |  ~~~
     | The term 'php' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the
     | spelling of the name, or if a path was included, verify that the path is correct and try again.

php: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:89
Line |
  89 |  php artisan migrate --force
     |  ~~~
     | The term 'php' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
php: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:89
Line |
  89 |  php artisan migrate --force
     |  ~~~
     | The term 'php' is not recognized as a name of a cmdlet, function, script file, or executable program. Check the
     | spelling of the name, or if a path was included, verify that the path is correct and try again.

processed file: C:\inetpub\wwwroot\bookstack\storage
processed file: C:\inetpub\wwwroot\bookstack\storage\app
processed file: C:\inetpub\wwwroot\bookstack\storage\backups
processed file: C:\inetpub\wwwroot\bookstack\storage\clockwork
processed file: C:\inetpub\wwwroot\bookstack\storage\fonts
processed file: C:\inetpub\wwwroot\bookstack\storage\framework
processed file: C:\inetpub\wwwroot\bookstack\storage\logs
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads
processed file: C:\inetpub\wwwroot\bookstack\storage\app\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\backups\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\clockwork\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\fonts\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\cache
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\sessions
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\views
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\cache\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\sessions\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\framework\views\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\logs\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads\files
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads\images
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads\files\.gitignore
processed file: C:\inetpub\wwwroot\bookstack\storage\uploads\images\.gitignore
Successfully processed 24 files; Failed processing 0 files
processed file: C:\inetpub\wwwroot\bookstack\bootstrap\cache
processed file: C:\inetpub\wwwroot\bookstack\bootstrap\cache\.gitignore
Successfully processed 2 files; Failed processing 0 files
WARNING: Module WebAdministration is loaded in Windows PowerShell using WinPSCompatSession remoting session; please note that all input and output of commands from this module will be deserialized objects. If you want to load this module into PowerShell please use 'Import-Module -SkipEditionCheck' syntax.
New-WebAppPool: Filename: 
Error: Cannot add duplicate collection entry of type 'add' with unique key attribute 'name' set to 'BookStackAppPool'
New-WebAppPool: Filename: 
Error: Cannot add duplicate collection entry of type 'add' with unique key attribute 'name' set to 'BookStackAppPool'


Set-ItemProperty: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:100
Line |
 100 |  Set-ItemProperty IIS:\AppPools\BookStackAppPool managedRuntimeVersion …
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.
Set-ItemProperty: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:100
Line |
 100 |  Set-ItemProperty IIS:\AppPools\BookStackAppPool managedRuntimeVersion …
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.


Name             ID   State      Physical Path                  Bindings
----             --   -----      -------------                  --------
BookStack        6    Stopped    C:\inetpub\wwwroot\bookstack\p
                                 ublic
New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:107
Line |
 107 |  New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.
New-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:107
Line |
 107 |  New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find drive. A drive with the name 'IIS' does not exist.

Install-PackageProvider: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:110
Line |
 110 |  Install-PackageProvider -Name NuGet -MinimumVersion 2.8.5.201 -Force  …
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | No match was found for the specified search criteria for the provider 'NuGet'. The package provider requires 'PackageManagement' and 'Provider' tags. Please check if the specified package has the tags.
Install-PackageProvider: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:110
Line |
 110 |  Install-PackageProvider -Name NuGet -MinimumVersion 2.8.5.201 -Force  …
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | No match was found for the specified search criteria for the provider 'NuGet'. The package provider requires
     | 'PackageManagement' and 'Provider' tags. Please check if the specified package has the tags.

Chocolatey v1.1.0
Installing the following packages:
urlrewrite
By installing, you accept licenses for the packages.
UrlRewrite v2.1.20190828 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - urlrewrite - UrlRewrite v2.1.20190828 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
>> TerminatingError(): "The pipeline has been stopped."
PS C:\inetpub\wwwroot\bookstack> cls
PS C:\inetpub\wwwroot\bookstack> cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> TerminatingError(Get-Command): "Cannot validate argument on parameter 'Name'. The argument is null, empty, or an element of the argument collection contains a null value. Supply a collection that does not contain any null values and then try the command again."
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> .\Install-BookStack-Complete.ps1
ParserError: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:152
Line |
 152 |  icacls "$bookstackFolder\storage"          /grant "$aclUser:(OI)(CI)( …
     |                                                     ~~~~~~~~~
     | Variable reference is not valid. ':' was not followed by a valid variable name character. Consider using ${} to delimit the name.
ParserError: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:152
Line |
 152 |  icacls "$bookstackFolder\storage"          /grant "$aclUser:(OI)(CI)( …
     |                                                     ~~~~~~~~~
     | Variable reference is not valid. ':' was not followed by a valid variable name character. Consider using ${} to
     | delimit the name.

PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> cd\
PS C:\> cd C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> TerminatingError(Get-Command): "Cannot validate argument on parameter 'Name'. The argument is null, empty, or an element of the argument collection contains a null value. Supply a collection that does not contain any null values and then try the command again."
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> .\Install-BookStack-Complete.ps1
ParserError: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:152
Line |
 152 |  icacls "$bookstackFolder\storage"          /grant "$aclUser:(OI)(CI)( …
     |                                                     ~~~~~~~~~
     | Variable reference is not valid. ':' was not followed by a valid variable name character. Consider using ${} to delimit the name.
ParserError: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:152
Line |
 152 |  icacls "$bookstackFolder\storage"          /grant "$aclUser:(OI)(CI)( …
     |                                                     ~~~~~~~~~
     | Variable reference is not valid. ':' was not followed by a valid variable name character. Consider using ${} to
     | delimit the name.

PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> cls
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> .\Install-BookStack-Complete.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Chocolatey v1.1.0
Installing the following packages:
vcredist140
By installing, you accept licenses for the packages.
vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - vcredist140 - vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
🔧 A instalar PHP…
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:76
Line |
  76 |  Copy-Item "$($phpRoot.FullName)\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php-8.3.23-devel-vs16-x64\php.ini-development' because it does not exist.
Copy-Item: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:76
Line |
  76 |  Copy-Item "$($phpRoot.FullName)\php.ini-development" $phpIni -Force
     |  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php-8.3.23-devel-vs16-x64\php.ini-development' because it does not exist.

Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:78
Line |
  78 |  (Get-Content $phpIni) |
     |   ~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php-8.3.23-devel-vs16-x64\php.ini' because it does not exist.
Get-Content: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:78
Line |
  78 |  (Get-Content $phpIni) |
     |   ~~~~~~~~~~~~~~~~~~~
     | Cannot find path 'C:\php\php-8.3.23-devel-vs16-x64\php.ini' because it does not exist.

🔧 A instalar Composer…
🛠️  A instalar MariaDB…
Chocolatey v1.1.0
Installing the following packages:
mariadb
By installing, you accept licenses for the packages.
mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - mariadb - mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:122
Line |
 122 |      || Stop-OnError "Erro a criar base de dados."
     |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Erro a criar base de dados.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:122
Line |
 122 |      || Stop-OnError "Erro a criar base de dados."
     |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Erro a criar base de dados.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> CLS
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> .\Install-BookStack-Complete.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Chocolatey v1.1.0
Installing the following packages:
vcredist140
By installing, you accept licenses for the packages.
vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - vcredist140 - vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Enjoy using Chocolatey? Explore more amazing features to take your
experience to the next level at
 https://chocolatey.org/compare
🔧 A instalar PHP…
WARNING: php.ini-development não existe (snapshot?). A criar php.ini vazio.
🔧 A instalar Composer…
🛠️  A instalar MariaDB…
Chocolatey v1.1.0
Installing the following packages:
mariadb
By installing, you accept licenses for the packages.
mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - mariadb - mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:136
Line |
 136 |      || Stop-OnError "Erro a criar base de dados."
     |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Erro a criar base de dados.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete.ps1:136
Line |
 136 |      || Stop-OnError "Erro a criar base de dados."
     |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Erro a criar base de dados.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> CLS
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> TerminatingError(Get-Command): "Cannot validate argument on parameter 'Name'. The argument is null, empty, or an element of the argument collection contains a null value. Supply a collection that does not contain any null values and then try the command again."
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> TerminatingError(Get-Command): "Cannot validate argument on parameter 'Name'. The argument is null, empty, or an element of the argument collection contains a null value. Supply a collection that does not contain any null values and then try the command again."
PS C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final> .\Install-BookStack-Complete2.ps1
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Transcript started, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
Chocolatey v1.1.0
Installing the following packages:
vcredist140
By installing, you accept licenses for the packages.
vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - vcredist140 - vcredist140 v14.32.31326 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.
🔧 A instalar PHP…
WARNING: php.ini-development ausente; a criar php.ini vazio.
🔧 A instalar Composer…
🛠️  A instalar MariaDB (nova)…
Chocolatey v1.1.0
Installing the following packages:
mariadb
By installing, you accept licenses for the packages.
mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Chocolatey installed 0/1 packages.
 See the log for details (C:\ProgramData\chocolatey\logs\chocolatey.log).

Warnings:
 - mariadb - mariadb v11.8.2 already installed.
 Use --force to reinstall, specify a version to install, or try upgrade.

Enjoy using Chocolatey? Explore more amazing features to take your
experience to the next level at
 https://chocolatey.org/compare
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete2.ps1:119
Line |
 119 |  … rd="$rootPassword" -e "$sql" || Stop-OnError "Erro ao configurar BD."
     |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Erro ao configurar BD.
Write-Error: C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\Install-BookStack-Complete2.ps1:119
Line |
 119 |  … rd="$rootPassword" -e "$sql" || Stop-OnError "Erro ao configurar BD."
     |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
     | ❌ Erro ao configurar BD.

Transcript stopped, output file is C:\GD\Docs_Tecnicos\Wiki\BookStack\BookStackInstallerPackage-Final\InstallLog.txt
