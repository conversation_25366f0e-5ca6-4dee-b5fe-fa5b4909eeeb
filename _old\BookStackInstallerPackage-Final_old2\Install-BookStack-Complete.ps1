
# Install-BookStack-Complete.ps1
# Script completo para instalação segura do BookStack no Windows com IIS, MariaDB e HTTPS

Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

function Stop-OnError($msg) {
    Write-Error $msg
    Stop-Transcript
    exit 1
}

# Pedir password ROOT de forma segura
$secureRootPassword = Read-Host "Introduz a password ROOT do MariaDB" -AsSecureString
$rootPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($secureRootPassword)
)

# Parar o log antes de gerar a password do utilizador
Stop-Transcript
$bookstackUserPassword = -join ((33..126) | Get-Random -Count 18 | ForEach-Object {[char]$_})
Start-Transcript -Path "$PSScriptRoot\InstallLog.txt" -Append

# Instalar Chocolatey se necessário
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# Instalar URL Rewrite
if (-not (Get-ChildItem "$env:SystemDrive\Windows\System32\inetsrv\rewrite.dll" -ErrorAction SilentlyContinue)) {
    choco install urlrewrite -y || Stop-OnError "❌ Falha ao instalar URL Rewrite"
}

# Instalar PHP dinamicamente
$phpPage = Invoke-WebRequest "https://windows.php.net/downloads/releases/" -UseBasicParsing
$phpLink = ($phpPage.Links | Where-Object href -Match '-Win32-vs16-x64.zip$' |
            Sort-Object href -Descending)[0].href
$phpZipUrl = "https://windows.php.net$phpLink"
$phpZip    = "$env:TEMP\php.zip"
$phpFolder = "C:\php"

Invoke-WebRequest $phpZipUrl -OutFile $phpZip -ErrorAction Stop
Expand-Archive $phpZip -DestinationPath $phpFolder -Force

# Criar php.ini com extensões necessárias
$phpIni = "$phpFolder\php.ini"
Copy-Item "$phpFolder\php.ini-development" $phpIni -Force
(gc $phpIni) | % {
    $_ -replace ';extension=', 'extension=' `
       -replace ';date.timezone =', 'date.timezone = Europe/Lisbon' `
       -replace 'upload_max_filesize = 2M', 'upload_max_filesize = 20M'
} | Set-Content $phpIni

# Adicionar PHP ao PATH
[Environment]::SetEnvironmentVariable("Path", "$phpFolder;$env:Path", "Machine")
$env:Path = "$phpFolder;$env:Path"

# Instalar Composer
$composerInstaller = "$env:TEMP\ComposerSetup.exe"
Invoke-WebRequest "https://getcomposer.org/Composer-Setup.exe" -OutFile $composerInstaller -ErrorAction Stop
Start-Process -FilePath $composerInstaller -ArgumentList "/quiet" -Wait
$composer = "$Env:ProgramFiles\ComposerSetup\bin\composer.bat"

# Instalar MariaDB
choco install mariadb -y --params "/Password:$rootPassword"

# Esperar que mysql esteja no PATH
$mysql = Get-Command mysql.exe -ErrorAction Stop | Select-Object -ExpandProperty Source

# Criar base de dados e utilizador
$script = @"
CREATE DATABASE bookstack CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'bookstack_user'@'localhost' IDENTIFIED BY '$bookstackUserPassword';
GRANT ALL PRIVILEGES ON bookstack.* TO 'bookstack_user'@'localhost';
FLUSH PRIVILEGES;
"@

& $mysql -u root --password="$rootPassword" -e "$script"

# Clonar BookStack
$bookstackFolder = "C:\inetpub\wwwroot\bookstack"
git clone https://github.com/BookStackApp/BookStack.git $bookstackFolder
Set-Location $bookstackFolder
& $composer install --no-dev

# Criar ficheiro .env
Copy-Item ".env.example" ".env" -Force
(gc ".env") | % {
    $_ -replace 'DB_DATABASE=.*', 'DB_DATABASE=bookstack' `
       -replace 'DB_USERNAME=.*', 'DB_USERNAME=bookstack_user' `
       -replace 'DB_PASSWORD=.*', "DB_PASSWORD=$bookstackUserPassword"
} | Set-Content ".env"

# Gerar chave e migrar
php artisan key:generate
php artisan migrate --force

# Conceder permissões
icacls "$bookstackFolder\storage" /grant "IIS_IUSRS:(OI)(CI)(M)" /T
icacls "$bookstackFolder\bootstrap\cache" /grant "IIS_IUSRS:(OI)(CI)(M)" /T

# Criar AppPool e site no IIS
Import-Module WebAdministration
New-WebAppPool -Name "BookStackAppPool"
Set-ItemProperty IIS:\AppPools\BookStackAppPool -Name "managedRuntimeVersion" -Value ""
Set-ItemProperty IIS:\AppPools\BookStackAppPool -Name "enable32BitAppOnWin64" -Value $false
New-Website -Name "BookStack" -Port 80 -IPAddress "*" -HostHeader "" `
            -PhysicalPath "$bookstackFolder\public" -ApplicationPool "BookStackAppPool"

# Criar certificado self-signed
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "Cert:\LocalMachine\My"
New-WebBinding -Name "BookStack" -Protocol https -Port 443 -IPAddress "*"
New-Item "IIS:\SslBindings\0.0.0.0!443" -Value $cert

# Redirecionamento HTTP -> HTTPS via web.config
Set-Content "$bookstackFolder\web.config" @"
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name='Redirect to HTTPS' stopProcessing='true'>
          <match url='(.*)' />
          <conditions>
            <add input='{HTTPS}' pattern='off' ignoreCase='true' />
          </conditions>
          <action type='Redirect' url='https://{HTTP_HOST}/{R:1}' redirectType='Permanent' />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
"@

# Fim do transcript
Stop-Transcript

Write-Host "`n✅ Instalação concluída com sucesso!" -ForegroundColor Green
Write-Host "`n🔐 Credenciais criadas:"
Write-Host "  -> Utilizador base de dados: bookstack_user"
Write-Host "  -> Password: $bookstackUserPassword"
Write-Host "  -> Acede a: https://localhost/bookstack (certificado self-signed)"
